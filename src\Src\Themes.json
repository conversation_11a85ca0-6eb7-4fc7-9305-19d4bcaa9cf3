{"CurrentTheme": "coralTheme", "CustomThemes": [{"name": "defaultTheme", "palette": [{"name": "ScreenBkgColor", "type": "c", "value": "%Color.RESERVED%.White"}, {"name": "InvertedBkgColor", "type": "c", "value": "RGBA(56, 96, 178, 1)"}, {"name": "PrimaryColor1", "type": "c", "value": "RGBA(56, 96, 178, 1)"}, {"name": "PrimaryColor2", "type": "c", "value": "RGBA(0, 18, 107, 1)"}, {"name": "PrimaryColor3", "type": "c", "value": "RGBA(186, 202, 226, 1)"}, {"name": "PrimaryColor1Light", "type": "c", "value": "RGBA(56, 96, 178, .2)"}, {"name": "PrimaryColor2Light", "type": "c", "value": "RGBA(0, 18, 107, .2)"}, {"name": "PrimaryColor3Light", "type": "c", "value": "RGBA(186, 202, 226, .2)"}, {"name": "PrimaryColor3Fade", "type": "c", "value": "ColorFade(RGBA(186, 202, 226, 1), 70%)"}, {"name": "Transparency", "type": "c", "value": "RGBA(0, 0, 0, 0)"}, {"name": "TextMainColor", "type": "c", "value": "RGBA(0, 0, 0, 1)"}, {"name": "TextMainColorInverted", "type": "c", "value": "RGBA(255, 255, 255, 1)"}, {"name": "TextLinkColor", "type": "c", "value": "RGBA(0, 134, 208, 1)"}, {"name": "TextFooterFontColor", "type": "c", "value": "RGBA(117, 117, 117, 1)"}, {"name": "InputBkgColor", "type": "c", "value": "RGBA(255, 255, 255, 1)"}, {"name": "InputTextColor", "type": "c", "value": "RGBA(0, 0, 0, 1)"}, {"name": "InputBorderColor", "type": "c", "value": "RGBA(0, 18, 107, 1)"}, {"name": "RailBkgColor", "type": "c", "value": "RGBA(128, 130, 133, 1)"}, {"name": "HandleBkgColor", "type": "c", "value": "RGBA(255, 255, 255, 1)"}, {"name": "InnerCircleBkgColor", "type": "c", "value": "RGBA(255, 255, 255, 1)"}, {"name": "DisabledBorderColor", "type": "c", "value": "RGBA(166, 166, 166, 1)"}, {"name": "DisabledTextMainColor", "type": "c", "value": "RGBA(166, 166, 166, 1)"}, {"name": "DisabledInputBkgColor", "type": "c", "value": "RGBA(244, 244, 244, 1)"}, {"name": "DisabledButtonBkgColor", "type": "c", "value": "RGBA(244, 244, 244, 1)"}, {"name": "HoverButtonBkgColor", "type": "c", "value": "ColorFade(RGBA(56, 96, 178, 1), -20%)"}, {"name": "HoverCancelButtonBkgColor", "type": "c", "value": "ColorFade(RGBA(62, 96, 170, 1), 20%)"}, {"name": "HoverInputBkgColor", "type": "c", "value": "RGBA(186, 202, 226, 1)"}, {"name": "OverlayBkgColor", "type": "c", "value": "RGBA(0, 0, 0, 0.4)"}, {"name": "ReservedInfoColor", "type": "c", "value": "RGBA(0, 134, 208, 1)"}, {"name": "ReservedSuccessColor", "type": "c", "value": "RGBA(141, 198, 63, 1)"}, {"name": "ReservedWarningColor", "type": "c", "value": "RGBA(252, 219, 2, 1)"}, {"name": "ReservedErrorColor", "type": "c", "value": "RGBA(246, 88, 16, 1)"}, {"name": "ReservedCriticalErrorColor", "type": "c", "value": "RGBA(168, 0, 0, 1)"}, {"name": "ReservedDisabledStatusColor", "type": "c", "value": "RGBA(193, 193, 193, 1)"}, {"name": "ReservedWhiteColor", "type": "c", "value": "RGBA(255, 255, 255, 1)"}, {"name": "ReservedGrayColor", "type": "c", "value": "RGBA(240, 240, 240, 1)"}, {"name": "ReservedBlackColor", "type": "c", "value": "RGBA(47, 41, 43, 1)"}, {"name": "ReservedChartColorSet", "type": "![]", "value": "[RGBA(49, 130, 93, 1),RGBA(48,166,103, 1), RGBA(94,193,108,1), RGBA(246,199,144,1), RGBA(247,199,114,1), RGBA(247,180,91,1), RGBA(246,143,100,1), RGBA(212,96,104,1), RGBA(148, 110, 176, 1), RGBA(118, 154, 204, 1), RGBA(96, 197, 234, 1)]"}, {"name": "TextBodyFontWeight", "type": "e", "value": "%FontWeight.RESERVED%.Normal"}, {"name": "TextEmphasisFontWeight", "type": "e", "value": "%FontWeight.RESERVED%.Semibold"}, {"name": "TextBodyFontFace", "type": "e", "value": "%Font.RESERVED%.'Open Sans'"}, {"name": "InputBorderThickness", "type": "n", "value": "2"}, {"name": "InputFocusedBorderThickness", "type": "n", "value": "4"}, {"name": "TextHeaderFontSize", "phoneValue": "27", "type": "n", "value": "18"}, {"name": "TextTitleFontSize", "type": "n", "value": "20"}, {"name": "TextSubtitleFontSize", "type": "n", "value": "18"}, {"name": "TextContentFontSize", "type": "n", "value": "16"}, {"name": "TextTitleFontSize_galleryLayouts_ver5", "type": "n", "value": "14"}, {"name": "TextSubtitleFontSize_galleryLayouts_ver5", "type": "n", "value": "12"}, {"name": "TextContentFontSize_galleryLayouts_ver5", "type": "n", "value": "12"}, {"name": "DividerColor2020", "type": "c", "value": "RGBA(255, 255, 255, 1)"}, {"name": "TextTitleColor_galleryLayouts_ver5", "type": "c", "value": "RGBA(50, 49, 48, 1)"}, {"name": "TableNameLabelPadding_copilotAppSinglePage", "type": "n", "value": "16"}, {"name": "SearchContainerFill_copilotAppPage", "type": "c", "value": "RGBA(245, 245, 245, 1)"}, {"name": "ContainerRadius", "type": "n", "value": "4"}, {"name": "TextHeaderFontSize2020", "type": "n", "value": "16"}, {"name": "TextEmphasisFontSize", "phoneValue": "24", "type": "n", "value": "15"}, {"name": "TextBodyFontSize", "phoneValue": "21", "type": "n", "value": "13"}, {"name": "TextFooterFontSize", "phoneValue": "18", "type": "n", "value": "11"}, {"name": "TextMiniFontSize", "phoneValue": "15", "type": "n", "value": "9"}, {"name": "IconFillColorInverted", "type": "c", "value": "RGBA(255, 255, 255, 1)"}, {"name": "IconPressedFillColorInverted", "type": "c", "value": "RGBA(255, 255, 255, 0.3)"}, {"name": "DatePickerSelectedColor", "type": "c", "value": "RGBA(37, 70, 148, 1)"}, {"name": "DatePickerHeaderColor", "type": "c", "value": "RGBA(68, 97, 165, 1)"}, {"name": "NoAttachmentPaddingLeft", "phoneValue": "20", "type": "n", "value": "12"}, {"name": "DefaultSize", "phoneValue": "24", "type": "n", "value": "14"}, {"name": "DefaultSize2", "type": "n", "value": "13"}, {"name": "DropTargetBorderColor", "type": "c", "value": "RGBA(0, 0, 0, 1)"}, {"name": "DropTargetBackgroundColor", "type": "c", "value": "RGBA(255, 255, 255, 0.8)"}, {"name": "DropTargetTextColor", "type": "c", "value": "RGBA(0, 0, 0, 1)"}, {"name": "DropTargetBorderThickness", "type": "n", "value": "2"}], "styles": [{"controlTemplateName": "screen", "name": "defaultScreenStyle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.ScreenBkgColor%"}, {"property": "LoadingSpinnerColor", "value": "%Palette.PrimaryColor1%"}]}, {"controlTemplateName": "label", "name": "defaultLabelStyle", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "Size", "value": "%Palette.TextBodyFontSize%"}, {"property": "LineHeight", "value": "1.2"}, {"property": "Overflow", "value": "%Overflow.RESERVED%.Hidden"}, {"property": "DisabledBorderColor", "value": "RGBA(56, 56, 56, 1)"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "FocusedBorderThickness", "value": "0"}, {"property": "Fill", "value": "RGBA(0, 0, 0, 0)"}, {"property": "DisabledFill", "value": "RGBA(0, 0, 0, 0)"}, {"property": "Italic", "value": "false"}, {"property": "Underline", "value": "false"}, {"property": "Strikethrough", "value": "false"}, {"property": "PaddingTop", "value": "5"}, {"property": "PaddingRight", "value": "5"}, {"property": "PaddingBottom", "value": "5"}, {"property": "PaddingLeft", "value": "5"}, {"property": "VerticalAlign", "value": "%VerticalAlign.RESERVED%.Middle"}]}, {"controlTemplateName": "label", "name": "basicNoSizeLabelStyle", "propertyValuesMap": [{"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}]}, {"controlTemplateName": "label", "name": "basicNoSizeInvertedBkgLabelStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColorInverted%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}]}, {"controlTemplateName": "label", "name": "basicNoSizeWeightColorLabelStyle", "propertyValuesMap": [{"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}]}, {"controlTemplateName": "label", "name": "invertedBkgHeaderLabelStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColorInverted%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "Size", "value": "%Palette.TextHeaderFontSize%"}]}, {"controlTemplateName": "label", "name": "invertedBkgTitleLabelStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColorInverted%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "Size", "value": "%Palette.TextTitleFontSize%"}]}, {"controlTemplateName": "label", "name": "linkLabelStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextLinkColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}]}, {"controlTemplateName": "label", "name": "headerLabelStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "Size", "value": "%Palette.TextHeaderFontSize%"}]}, {"controlTemplateName": "label", "name": "subHeaderLabelStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.PrimaryColor2%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}, {"property": "Size", "value": "%Palette.TextBodyFontSize%"}]}, {"controlTemplateName": "label", "name": "titleLabelStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}, {"property": "Size", "value": "%Palette.TextTitleFontSize%"}]}, {"controlTemplateName": "label", "name": "overlayTitleLabelStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColorInverted%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}, {"property": "Size", "value": "%Palette.TextTitleFontSize%"}]}, {"controlTemplateName": "label", "name": "subtitleLabelStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "Size", "value": "%Palette.TextSubtitleFontSize%"}]}, {"controlTemplateName": "label", "name": "overlaySubtitleLabelStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColorInverted%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "Size", "value": "%Palette.TextSubtitleFontSize%"}]}, {"controlTemplateName": "label", "name": "contentLabelStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "Size", "value": "%Palette.TextContentFontSize%"}]}, {"controlTemplateName": "label", "name": "titleLabelStyle_galleryLayouts_ver5", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextTitleColor_galleryLayouts_ver5%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "Size", "value": "%Palette.TextTitleFontSize_galleryLayouts_ver5%"}]}, {"controlTemplateName": "label", "name": "subtitleLabelStyle_galleryLayouts_ver5", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "Size", "value": "%Palette.TextSubtitleFontSize_galleryLayouts_ver5%"}]}, {"controlTemplateName": "label", "name": "contentLabelStyle_galleryLayouts_ver5", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "Size", "value": "%Palette.TextContentFontSize_galleryLayouts_ver5%"}]}, {"controlTemplateName": "rectangle", "name": "dividerStyle2020", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.DividerColor2020%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"controlTemplateName": "label", "name": "tableNameLabelStyle_copilotAppSinglePage", "propertyValuesMap": [{"property": "Color", "value": "%Palette.ReservedWhiteColor%"}, {"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "PaddingLeft", "value": "%Palette.TableNameLabelPadding_copilotAppSinglePage%"}, {"property": "PaddingRight", "value": "%Palette.TableNameLabelPadding_copilotAppSinglePage%"}, {"property": "PaddingTop", "value": "%Palette.TableNameLabelPadding_copilotAppSinglePage%"}, {"property": "PaddingBottom", "value": "%Palette.TableNameLabelPadding_copilotAppSinglePage%"}]}, {"controlTemplateName": "groupContainer", "name": "containerStyle_copilotAppPage", "propertyValuesMap": [{"property": "DropShadow", "value": "%DropShadow.RESERVED%.None"}]}, {"controlTemplateName": "groupContainer", "name": "searchContainerStyle_copilotAppPage", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.SearchContainerFill_copilotAppPage%"}]}, {"controlTemplateName": "text", "name": "searchInputStyle_copilotAppPage", "propertyValuesMap": [{"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.None"}, {"property": "Fill", "value": "%Palette.Transparency%"}, {"property": "HoverFill", "value": "%Palette.Transparency%"}, {"property": "PressedFill", "value": "%Palette.Transparency%"}]}, {"controlTemplateName": "icon", "name": "searchIconStyle_copilotAppPage", "propertyValuesMap": [{"property": "Color", "value": "%Palette.PrimaryColor1%"}, {"property": "Fill", "value": "%Palette.Transparency%"}]}, {"controlTemplateName": "icon", "name": "headerIconStyle_copilotAppSinglePage", "propertyValuesMap": [{"property": "Color", "value": "%Palette.ReservedWhiteColor%"}, {"property": "Fill", "value": "%Palette.PrimaryColor1%"}]}, {"controlTemplateName": "label", "name": "accentLabelStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.PrimaryColor2%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "Size", "value": "%Palette.TextBodyFontSize%"}, {"property": "LineHeight", "value": "1.2"}, {"property": "Overflow", "value": "%Overflow.RESERVED%.Hidden"}, {"property": "DisabledBorderColor", "value": "RGBA(56, 56, 56, 1)"}, {"property": "BorderColor", "value": "RGBA(0, 0, 0, 1)"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "FocusedBorderThickness", "value": "0"}, {"property": "Fill", "value": "RGBA(0, 0, 0, 0)"}, {"property": "DisabledFill", "value": "RGBA(0, 0, 0, 0)"}, {"property": "Italic", "value": "false"}, {"property": "Underline", "value": "false"}, {"property": "Strikethrough", "value": "false"}, {"property": "PaddingTop", "value": "5"}, {"property": "PaddingRight", "value": "5"}, {"property": "PaddingBottom", "value": "5"}, {"property": "PaddingLeft", "value": "5"}, {"property": "VerticalAlign", "value": "%VerticalAlign.RESERVED%.Middle"}]}, {"controlTemplateName": "label", "name": "pickerEmphasisLabelStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}, {"property": "Size", "value": "%Palette.TextEmphasisFontSize%"}]}, {"controlTemplateName": "label", "name": "pickerEmphasisWithAccentLabelStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.PrimaryColor2%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}, {"property": "Size", "value": "%Palette.TextEmphasisFontSize%"}]}, {"controlTemplateName": "label", "name": "pickerEmphasisSecondaryLabelStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextFooterFontColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "Size", "value": "%Palette.TextEmphasisFontSize%"}]}, {"controlTemplateName": "label", "name": "footerAccentLabelStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.PrimaryColor2%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "Size", "value": "%Palette.TextFooterFontSize%"}]}, {"controlTemplateName": "label", "name": "footerLabelStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextFooterFontColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "Size", "value": "%Palette.TextFooterFontSize%"}, {"property": "BorderColor", "value": "RGBA(0, 0, 0, 1)"}, {"property": "LineHeight", "value": "1.2"}, {"property": "Overflow", "value": "%Overflow.RESERVED%.Hidden"}, {"property": "DisabledBorderColor", "value": "RGBA(56, 56, 56, 1)"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "FocusedBorderThickness", "value": "0"}, {"property": "Fill", "value": "RGBA(0, 0, 0, 0)"}, {"property": "DisabledFill", "value": "RGBA(0, 0, 0, 0)"}, {"property": "Italic", "value": "false"}, {"property": "Underline", "value": "false"}, {"property": "Strikethrough", "value": "false"}, {"property": "PaddingTop", "value": "5"}, {"property": "PaddingRight", "value": "5"}, {"property": "PaddingBottom", "value": "5"}, {"property": "PaddingLeft", "value": "5"}, {"property": "VerticalAlign", "value": "%VerticalAlign.RESERVED%.Middle"}]}, {"controlTemplateName": "label", "name": "miniLabelStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.PrimaryColor2%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "Size", "value": "%Palette.TextMiniFontSize%"}]}, {"controlTemplateName": "label", "name": "miniInvertedBkgLabelStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColorInverted%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "Size", "value": "%Palette.TextMiniFontSize%"}]}, {"controlTemplateName": "label", "name": "disabled<PERSON><PERSON><PERSON><PERSON><PERSON>le", "propertyValuesMap": [{"property": "Color", "value": "%Palette.ReservedDisabledStatusColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}]}, {"controlTemplateName": "label", "name": "infoLabelStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextLinkColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}]}, {"controlTemplateName": "label", "name": "successLabelStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.ReservedSuccessColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}]}, {"controlTemplateName": "label", "name": "warningLabelStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.ReservedWarningColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}]}, {"controlTemplateName": "label", "name": "errorLabelStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.ReservedErrorColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}]}, {"controlTemplateName": "label", "name": "criticalErrorLabelStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.ReservedCriticalErrorColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}, {"property": "LineHeight", "value": "1.2"}, {"property": "BorderColor", "value": "RGBA(0, 0, 0, 1)"}, {"property": "Overflow", "value": "%Overflow.RESERVED%.Hidden"}, {"property": "DisabledBorderColor", "value": "RGBA(56, 56, 56, 1)"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "FocusedBorderThickness", "value": "0"}, {"property": "Fill", "value": "RGBA(0, 0, 0, 0)"}, {"property": "DisabledFill", "value": "RGBA(0, 0, 0, 0)"}, {"property": "Italic", "value": "false"}, {"property": "Underline", "value": "false"}, {"property": "Strikethrough", "value": "false"}, {"property": "VerticalAlign", "value": "%VerticalAlign.RESERVED%.Middle"}, {"property": "Size", "value": "%Palette.DefaultSize%"}]}, {"controlTemplateName": "toggleSwitch", "name": "defaultToggleSwitchStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "HandleFill", "value": "%Palette.HandleBkgColor%"}, {"property": "FalseFill", "value": "%Palette.RailBkgColor%"}, {"property": "Size", "value": "%Palette.TextBodyFontSize%"}, {"property": "TrueFill", "value": "%Palette.PrimaryColor1%"}, {"property": "FalseHoverFill", "value": "ColorFade(Self.FalseFill, 15%)"}, {"property": "TrueHoverFill", "value": "ColorFade(<PERSON><PERSON>Fill, 15%)"}, {"property": "DisabledBorderColor", "value": "RGBA(56, 56, 56, 1)"}, {"property": "PressedBorderColor", "value": "ColorFade(Self.BorderColor, -15%)"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 15%)"}, {"property": "BorderThickness", "value": "0"}, {"property": "FocusedBorderThickness", "value": "2"}]}, {"controlTemplateName": "rating", "name": "defaultRatingStyle", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "RatingFill", "value": "%Palette.PrimaryColor2%"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "Fill", "value": "RGBA(0, 0, 0, 0)"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "FocusedBorderThickness", "value": "2"}]}, {"controlTemplateName": "checkbox", "name": "defaultCheckboxStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "CheckboxBackgroundFill", "value": "%Palette.InnerCircleBkgColor%"}, {"property": "CheckboxBorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "CheckmarkFill", "value": "%Palette.InputTextColor%"}, {"property": "HoverColor", "value": "%Palette.PrimaryColor2%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "Size", "value": "%Palette.TextBodyFontSize%"}, {"property": "DisabledBorderColor", "value": "RGBA(56, 56, 56, 1)"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "FocusedBorderThickness", "value": "2"}, {"property": "PressedBorderColor", "value": "ColorFade(Self.BorderColor, -30%)"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 30%)"}, {"property": "DisabledColor", "value": "RGBA(186, 186, 186, 1)"}, {"property": "Fill", "value": "RGBA(0, 0, 0, 0)"}, {"property": "DisabledFill", "value": "RGBA(0, 0, 0, 0)"}, {"property": "PressedColor", "value": "RGBA(70, 68, 64, 1)"}, {"property": "PressedFill", "value": "ColorFade(Self<PERSON>ll, -30%)"}, {"property": "HoverFill", "value": "ColorFade(Self.Fill, 30%)"}, {"property": "Italic", "value": "false"}, {"property": "Underline", "value": "false"}, {"property": "Strikethrough", "value": "false"}, {"property": "PaddingTop", "value": "0"}, {"property": "PaddingRight", "value": "0"}, {"property": "PaddingBottom", "value": "0"}, {"property": "PaddingLeft", "value": "0"}, {"property": "VerticalAlign", "value": "%VerticalAlign.RESERVED%.Middle"}]}, {"controlTemplateName": "radio", "name": "defaultRadioStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "HoverColor", "value": "%Palette.PrimaryColor2%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "RadioBackgroundFill", "value": "%Palette.InnerCircleBkgColor%"}, {"property": "RadioBorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "RadioSelectionFill", "value": "%Palette.InputTextColor%"}, {"property": "Size", "value": "%Palette.TextBodyFontSize%"}, {"property": "DisabledBorderColor", "value": "RGBA(56, 56, 56, 1)"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "FocusedBorderThickness", "value": "2"}, {"property": "DisabledColor", "value": "RGBA(186, 186, 186, 1)"}, {"property": "Fill", "value": "RGBA(0, 0, 0, 0)"}, {"property": "DisabledFill", "value": "RGBA(0, 0, 0, 0)"}, {"property": "Italic", "value": "false"}, {"property": "Underline", "value": "false"}, {"property": "Strikethrough", "value": "false"}, {"property": "PaddingTop", "value": "10"}, {"property": "PaddingRight", "value": "0"}, {"property": "PaddingBottom", "value": "10"}, {"property": "PaddingLeft", "value": "0"}, {"property": "Align", "value": "%Align.RESERVED%.Left"}, {"property": "PressedColor", "value": "Self.Color"}, {"property": "HoverFill", "value": "Self<PERSON>ll"}, {"property": "PressedFill", "value": "Self<PERSON>ll"}]}, {"controlTemplateName": "listbox", "name": "defaultListboxStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.InputTextColor%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "BorderThickness", "value": "%Palette.InputBorderThickness%"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "FocusedBorderThickness", "value": "%Palette.InputFocusedBorderThickness%"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "DisabledSelectionColor", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "DisabledSelectionFill", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Fill", "value": "%Palette.InputBkgColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "HoverColor", "value": "%Palette.InputTextColor%"}, {"property": "HoverFill", "value": "%Palette.HoverInputBkgColor%"}, {"property": "PressedColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "PressedFill", "value": "%Palette.PrimaryColor2%"}, {"property": "SelectionColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "SelectionFill", "value": "%Palette.PrimaryColor1%"}, {"property": "Size", "value": "%Palette.TextBodyFontSize%"}, {"property": "DisabledColor", "value": "RGBA(186, 186, 186, 1)"}, {"property": "DisabledFill", "value": "RGBA(242, 242, 242, 1)"}, {"property": "PressedBorderColor", "value": "Self.HoverBorderColor"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 15%)"}, {"property": "PaddingTop", "value": "0"}, {"property": "PaddingRight", "value": "0"}, {"property": "PaddingBottom", "value": "0"}, {"property": "PaddingLeft", "value": "0"}]}, {"controlTemplateName": "dropdown", "name": "defaultDropdownStyle", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "%Palette.InputBorderThickness%"}, {"property": "FocusedBorderThickness", "value": "%Palette.InputFocusedBorderThickness%"}, {"property": "Color", "value": "%Palette.InputTextColor%"}, {"property": "ChevronBackground", "value": "%Palette.PrimaryColor1%"}, {"property": "ChevronFill", "value": "%Palette.TextMainColorInverted%"}, {"property": "ChevronHoverBackground", "value": "%Palette.HoverButtonBkgColor%"}, {"property": "ChevronHoverFill", "value": "%Palette.TextMainColorInverted%"}, {"property": "ChevronDisabledBackground", "value": "%Palette.DisabledBorderColor%"}, {"property": "ChevronDisabledFill", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "DisabledFill", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "Fill", "value": "%Palette.InputBkgColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "HoverColor", "value": "%Palette.InputTextColor%"}, {"property": "HoverFill", "value": "%Palette.HoverInputBkgColor%"}, {"property": "Size", "value": "%Palette.TextBodyFontSize%"}, {"property": "PressedColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "PressedFill", "value": "%Palette.PrimaryColor2%"}, {"property": "SelectionColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "SelectionFill", "value": "%Palette.PrimaryColor1%"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 15%)"}, {"property": "PressedBorderColor", "value": "Self.HoverBorderColor"}, {"property": "Italic", "value": "false"}, {"property": "Underline", "value": "false"}, {"property": "Strikethrough", "value": "false"}, {"property": "PaddingTop", "value": "10"}, {"property": "PaddingRight", "value": "10"}, {"property": "PaddingBottom", "value": "10"}, {"property": "PaddingLeft", "value": "10"}]}, {"controlTemplateName": "combobox", "name": "defaultComboBoxStyle", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "%Palette.InputBorderThickness%"}, {"property": "FocusedBorderThickness", "value": "%Palette.InputFocusedBorderThickness%"}, {"property": "Color", "value": "%Palette.InputTextColor%"}, {"property": "ChevronBackground", "value": "%Palette.PrimaryColor1%"}, {"property": "ChevronFill", "value": "%Palette.TextMainColorInverted%"}, {"property": "ChevronHoverBackground", "value": "%Palette.HoverButtonBkgColor%"}, {"property": "ChevronHoverFill", "value": "%Palette.TextMainColorInverted%"}, {"property": "ChevronDisabledBackground", "value": "%Palette.DisabledBorderColor%"}, {"property": "ChevronDisabledFill", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "DisabledFill", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "Fill", "value": "%Palette.InputBkgColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "HoverColor", "value": "%Palette.InputTextColor%"}, {"property": "HoverFill", "value": "%Palette.HoverInputBkgColor%"}, {"property": "Size", "value": "%Palette.TextBodyFontSize%"}, {"property": "PressedColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "PressedFill", "value": "%Palette.PrimaryColor2%"}, {"property": "SelectionColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "SelectionFill", "value": "%Palette.PrimaryColor1%"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 15%)"}, {"property": "PressedBorderColor", "value": "Self.HoverBorderColor"}, {"property": "MoreItemsButtonColor", "value": "Self.ChevronBackground"}]}, {"controlTemplateName": "attachments", "name": "defaultAttachmentsStyle", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "%Palette.InputBorderThickness%"}, {"property": "FocusedBorderThickness", "value": "%Palette.InputFocusedBorderThickness%"}, {"property": "Color", "value": "%Palette.InputTextColor%"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "DisabledFill", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "Fill", "value": "%Palette.InputBkgColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "HoverColor", "value": "%Palette.InputTextColor%"}, {"property": "HoverFill", "value": "%Palette.HoverInputBkgColor%"}, {"property": "Size", "value": "%Palette.TextBodyFontSize%"}, {"property": "PressedColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "PressedFill", "value": "%Palette.PrimaryColor2%"}, {"property": "ItemColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "ItemFill", "value": "%Palette.PrimaryColor1%"}, {"property": "ItemHoverColor", "value": "%Palette.InputTextColor%"}, {"property": "ItemHoverFill", "value": "%Palette.HoverInputBkgColor%"}, {"property": "ItemSpacing", "value": "0"}, {"property": "NoAttachmentsColor", "value": "Self.Color"}, {"property": "NoAttachmentsPaddingLeft", "value": "%Palette.NoAttachmentPaddingLeft%"}, {"property": "DropTargetBorderThickness", "value": "%Palette.DropTargetBorderThickness%"}, {"property": "DropTargetBorderStyle", "value": "%BorderStyle.RESERVED%.Dotted"}, {"property": "DropTargetBorderColor", "value": "%Palette.DropTargetBorderColor%"}, {"property": "DropTargetBackgroundColor", "value": "%Palette.DropTargetBackgroundColor%"}, {"property": "DropTargetTextColor", "value": "%Palette.DropTargetTextColor%"}]}, {"controlTemplateName": "datepicker", "name": "defaultDatePickerStyle", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "%Palette.InputBorderThickness%"}, {"property": "FocusedBorderThickness", "value": "%Palette.InputFocusedBorderThickness%"}, {"property": "Color", "value": "%Palette.InputTextColor%"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "DisabledFill", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "Fill", "value": "%Palette.InputBkgColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "IconFill", "value": "%Palette.TextMainColorInverted%"}, {"property": "IconBackground", "value": "%Palette.PrimaryColor1%"}, {"property": "SelectedDateFill", "value": "%Palette.PrimaryColor1%"}, {"property": "HoverDateFill", "value": "%Palette.PrimaryColor3%"}, {"property": "CalendarHeaderFill", "value": "%Palette.PrimaryColor1%"}, {"property": "Size", "value": "%Palette.DefaultSize%"}, {"property": "Italic", "value": "false"}, {"property": "PaddingTop", "value": "0"}, {"property": "PaddingRight", "value": "5"}, {"property": "PaddingBottom", "value": "5"}, {"property": "PaddingLeft", "value": "12"}]}, {"controlTemplateName": "lookup", "name": "defaultLookupStyle", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "%Palette.InputBorderThickness%"}, {"property": "FocusedBorderThickness", "value": "%Palette.InputFocusedBorderThickness%"}, {"property": "Color", "value": "%Palette.InputTextColor%"}, {"property": "ChevronBackground", "value": "%Palette.PrimaryColor1%"}, {"property": "ChevronFill", "value": "%Palette.TextMainColorInverted%"}, {"property": "ChevronHoverBackground", "value": "%Palette.HoverButtonBkgColor%"}, {"property": "ChevronHoverFill", "value": "%Palette.TextMainColorInverted%"}, {"property": "ChevronDisabledBackground", "value": "%Palette.DisabledBorderColor%"}, {"property": "ChevronDisabledFill", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "DisabledFill", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "Fill", "value": "%Palette.InputBkgColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "HoverColor", "value": "%Palette.InputTextColor%"}, {"property": "HoverFill", "value": "%Palette.HoverInputBkgColor%"}, {"property": "Size", "value": "%Palette.TextBodyFontSize%"}, {"property": "PressedColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "PressedFill", "value": "%Palette.PrimaryColor2%"}, {"property": "SelectionColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "SelectionFill", "value": "%Palette.PrimaryColor1%"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 15%)"}, {"property": "PressedBorderColor", "value": "Self.HoverBorderColor"}, {"property": "Italic", "value": "false"}, {"property": "Underline", "value": "false"}, {"property": "Strikethrough", "value": "false"}, {"property": "PaddingTop", "value": "10"}, {"property": "PaddingRight", "value": "10"}, {"property": "PaddingBottom", "value": "10"}, {"property": "PaddingLeft", "value": "10"}, {"property": "FooterSize", "value": "Self.Size - 3"}]}, {"controlTemplateName": "text", "name": "defaultTextStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.InputTextColor%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "%Palette.InputBorderThickness%"}, {"property": "FocusedBorderThickness", "value": "%Palette.InputFocusedBorderThickness%"}, {"property": "HoverBorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "HoverColor", "value": "%Palette.InputTextColor%"}, {"property": "HoverFill", "value": "%Palette.HoverInputBkgColor%"}, {"property": "Fill", "value": "%Palette.InputBkgColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "DisabledFill", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "Size", "value": "%Palette.TextBodyFontSize%"}, {"property": "RadiusTopLeft", "value": "5"}, {"property": "RadiusBottomRight", "value": "5"}, {"property": "RadiusTopRight", "value": "5"}, {"property": "RadiusBottomLeft", "value": "5"}, {"property": "PressedBorderColor", "value": "Self.HoverBorderColor"}, {"property": "PressedColor", "value": "Self.Color"}, {"property": "PressedFill", "value": "Self<PERSON>ll"}, {"property": "Italic", "value": "false"}, {"property": "Underline", "value": "false"}, {"property": "Strikethrough", "value": "false"}, {"property": "Align", "value": "%Align.RESERVED%.Left"}]}, {"controlTemplateName": "text", "name": "searchTextStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.InputTextColor%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.None"}, {"property": "BorderThickness", "value": "%Palette.InputBorderThickness%"}, {"property": "FocusedBorderThickness", "value": "%Palette.InputFocusedBorderThickness%"}, {"property": "HoverBorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "HoverColor", "value": "%Palette.InputTextColor%"}, {"property": "HoverFill", "value": "%Palette.HoverInputBkgColor%"}, {"property": "Fill", "value": "%Palette.InputBkgColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "DisabledFill", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "Size", "value": "%Palette.TextBodyFontSize%"}]}, {"controlTemplateName": "slider", "name": "defaultSliderStyle", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "HandleFill", "value": "%Palette.HandleBkgColor%"}, {"property": "RailFill", "value": "%Palette.RailBkgColor%"}, {"property": "ValueFill", "value": "%Palette.PrimaryColor2%"}, {"property": "HandleHoverFill", "value": "<PERSON><PERSON>"}, {"property": "HandleActiveFill", "value": "<PERSON><PERSON>"}, {"property": "RailHoverFill", "value": "ColorFade(Self.RailFill, 15%)"}, {"property": "ValueHoverFill", "value": "ColorFade(Self.ValueFill, 15%)"}, {"property": "PressedBorderColor", "value": "ColorFade(Self.BorderColor, -30%)"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 30%)"}, {"property": "BorderThickness", "value": "0"}, {"property": "FocusedBorderThickness", "value": "2"}]}, {"controlTemplateName": "button", "name": "defaultButtonStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColorInverted%"}, {"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "DisabledFill", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "Size", "value": "%Palette.TextEmphasisFontSize%"}, {"property": "HoverColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "HoverFill", "value": "%Palette.HoverButtonBkgColor%"}, {"property": "BorderColor", "value": "ColorFade(Self.Fill, -15%)"}, {"property": "RadiusTopLeft", "value": "10"}, {"property": "RadiusBottomRight", "value": "10"}, {"property": "RadiusTopRight", "value": "10"}, {"property": "RadiusBottomLeft", "value": "10"}, {"property": "PressedBorderColor", "value": "Self<PERSON>ll"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 20%)"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "2"}, {"property": "FocusedBorderThickness", "value": "4"}, {"property": "PressedColor", "value": "Self<PERSON>ll"}, {"property": "PressedFill", "value": "Self.Color"}, {"property": "Italic", "value": "false"}, {"property": "Underline", "value": "false"}, {"property": "Strikethrough", "value": "false"}, {"property": "Align", "value": "%Align.RESERVED%.Center"}, {"property": "VerticalAlign", "value": "%VerticalAlign.RESERVED%.Middle"}]}, {"controlTemplateName": "button", "name": "cancelButtonStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.PrimaryColor1%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "DisabledFill", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "Fill", "value": "%Palette.PrimaryColor3%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}, {"property": "Size", "value": "%Palette.TextEmphasisFontSize%"}]}, {"controlTemplateName": "button", "name": "rezervedOkButtonStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.ReservedWhiteColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "DisabledFill", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "Fill", "value": "%Palette.ReservedInfoColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}, {"property": "Size", "value": "%Palette.TextEmphasisFontSize%"}]}, {"controlTemplateName": "button", "name": "rezervedCancelButtonStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.ReservedInfoColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "DisabledFill", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "Fill", "value": "%Palette.ReservedWhiteColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}, {"property": "Size", "value": "%Palette.TextEmphasisFontSize%"}]}, {"controlTemplateName": "lineChart", "name": "defaultLineChartStyle", "propertyValuesMap": [{"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "ItemColorSet", "value": "%Palette.ReservedChartColorSet%"}, {"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "BorderColor", "value": "RGBA(0, 0, 0, 1)"}, {"property": "PressedBorderColor", "value": "ColorFade(Self.BorderColor, -30%)"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 30%)"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "Font", "value": "%Font.RESERVED%.'Open Sans'"}, {"property": "Size", "value": "11"}]}, {"controlTemplateName": "lineChart", "name": "monochromeAccentLineChartStyle", "propertyValuesMap": [{"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "ItemColorSet", "value": "[%Palette.PrimaryColor1%]"}, {"property": "Color", "value": "%Palette.TextMainColor%"}]}, {"controlTemplateName": "<PERSON><PERSON><PERSON>", "name": "defaultPieChartStyle", "propertyValuesMap": [{"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "ItemColorSet", "value": "%Palette.ReservedChartColorSet%"}, {"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "BorderColor", "value": "RGBA(0, 0, 0, 1)"}, {"property": "PressedBorderColor", "value": "ColorFade(Self.BorderColor, -30%)"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 30%)"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "Font", "value": "%Font.RESERVED%.'Open Sans'"}, {"property": "Size", "value": "10"}]}, {"controlTemplateName": "<PERSON><PERSON><PERSON>", "name": "monochromeAccentPieChartStyle", "propertyValuesMap": [{"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "ItemColorSet", "value": "[%Palette.PrimaryColor1%]"}, {"property": "Color", "value": "%Palette.TextMainColor%"}]}, {"controlTemplateName": "<PERSON><PERSON><PERSON>", "name": "defaultBarChartStyle", "propertyValuesMap": [{"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "ItemColorSet", "value": "%Palette.ReservedChartColorSet%"}, {"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "BorderColor", "value": "RGBA(0, 0, 0, 1)"}, {"property": "PressedBorderColor", "value": "ColorFade(Self.BorderColor, -30%)"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 30%)"}, {"property": "Size", "value": "10"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "Font", "value": "%Font.RESERVED%.'Open Sans'"}]}, {"controlTemplateName": "<PERSON><PERSON><PERSON>", "name": "monochromeAccentBarChartStyle", "propertyValuesMap": [{"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "ItemColorSet", "value": "[%Palette.PrimaryColor1%]"}, {"property": "Color", "value": "%Palette.TextMainColor%"}]}, {"controlTemplateName": "legend", "name": "defaultLegendStyle", "propertyValuesMap": [{"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "Size", "value": "%Palette.TextFooterFontSize%"}, {"property": "BorderColor", "value": "RGBA(0, 0, 0, 1)"}, {"property": "DisabledBorderColor", "value": "RGBA(56, 56, 56, 1)"}, {"property": "PressedBorderColor", "value": "Self.BorderColor"}, {"property": "HoverBorderColor", "value": "Self.BorderColor"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "DisabledFill", "value": "RGBA(0, 0, 0, 0)"}, {"property": "Italic", "value": "false"}]}, {"controlTemplateName": "rectangle", "name": "separatorShapeStyle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor3%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"controlTemplateName": "videoPlayback", "name": "defaultVideoPlaybackStyle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}]}, {"controlTemplateName": "timer", "name": "defaultTimerStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColorInverted%"}, {"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "HoverFill", "value": "%Palette.HoverButtonBkgColor%"}, {"property": "HoverColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "BorderColor", "value": "ColorFade(Self.Fill, -15%)"}, {"property": "DisabledBorderColor", "value": "ColorFade(Self.BorderColor, 70%)"}, {"property": "PressedBorderColor", "value": "Self<PERSON>ll"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 20%)"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "2"}, {"property": "FocusedBorderThickness", "value": "4"}, {"property": "DisabledColor", "value": "ColorFade(Self.Fill, 90%)"}, {"property": "PressedColor", "value": "Self<PERSON>ll"}, {"property": "DisabledFill", "value": "ColorFade(Self.Fill, 70%)"}, {"property": "PressedFill", "value": "Self.Color"}, {"property": "Size", "value": "%Palette.DefaultSize2%"}]}, {"controlTemplateName": "triangle", "name": "defaultTriangleStyle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "DisabledFill", "value": "Self<PERSON>ll"}, {"property": "PressedFill", "value": "Self<PERSON>ll"}, {"property": "HoverFill", "value": "Self<PERSON>ll"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "FocusedBorderThickness", "value": "2"}]}, {"controlTemplateName": "star", "name": "defaultStarStyle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"controlTemplateName": "pentagon", "name": "defaultPentagonStyle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "DisabledFill", "value": "Self<PERSON>ll"}, {"property": "PressedFill", "value": "Self<PERSON>ll"}, {"property": "HoverFill", "value": "Self<PERSON>ll"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "FocusedBorderThickness", "value": "2"}]}, {"controlTemplateName": "partialCircle", "name": "defaultPartialCircleStyle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"controlTemplateName": "octagon", "name": "defaultOctagonStyle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "DisabledFill", "value": "Self<PERSON>ll"}, {"property": "PressedFill", "value": "Self<PERSON>ll"}, {"property": "HoverFill", "value": "Self<PERSON>ll"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "FocusedBorderThickness", "value": "2"}]}, {"controlTemplateName": "hexagon", "name": "defaultHexagonStyle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "DisabledFill", "value": "Self<PERSON>ll"}, {"property": "PressedFill", "value": "Self<PERSON>ll"}, {"property": "HoverFill", "value": "Self<PERSON>ll"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "FocusedBorderThickness", "value": "2"}]}, {"controlTemplateName": "hexagon", "name": "primary2HexagonStyle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor2%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"controlTemplateName": "hexagon", "name": "primary3HexagonStyle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor3%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"controlTemplateName": "hexagon", "name": "primary3FadeHexagonStyle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor3Fade%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"controlTemplateName": "hexagon", "name": "screenHexagonStyle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.ScreenBkgColor%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"controlTemplateName": "circle", "name": "defaultCircleStyle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "DisabledFill", "value": "Self<PERSON>ll"}, {"property": "PressedFill", "value": "Self<PERSON>ll"}, {"property": "HoverFill", "value": "Self<PERSON>ll"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}]}, {"controlTemplateName": "circle", "name": "primary2CircleStyle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor2%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"controlTemplateName": "circle", "name": "primary3CircleStyle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor3%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"controlTemplateName": "circle", "name": "primary3FadeCircleStyle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor3Fade%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"controlTemplateName": "arrow", "name": "defaultArrowStyle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}]}, {"controlTemplateName": "icon", "name": "defaultIconStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.PrimaryColor2%"}, {"property": "DisabledColor", "value": "%Palette.DisabledButtonBkgColor%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "PressedColor", "value": "ColorFade(Self.Color, -20%)"}, {"property": "HoverColor", "value": "ColorFade(Self.Color, 20%)"}, {"property": "Fill", "value": "RGBA(0, 0, 0, 0)"}, {"property": "DisabledFill", "value": "Self<PERSON>ll"}, {"property": "PressedFill", "value": "Self<PERSON>ll"}, {"property": "HoverFill", "value": "Self<PERSON>ll"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "PressedBorderColor", "value": "ColorFade(Self.BorderColor, -20%)"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 20%)"}, {"property": "BorderThickness", "value": "0"}, {"property": "FocusedBorderThickness", "value": "2"}]}, {"controlTemplateName": "icon", "name": "primary1IconStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.PrimaryColor1%"}, {"property": "DisabledColor", "value": "%Palette.DisabledButtonBkgColor%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}]}, {"controlTemplateName": "icon", "name": "primary3IconStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.PrimaryColor3%"}, {"property": "DisabledColor", "value": "%Palette.DisabledButtonBkgColor%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}]}, {"controlTemplateName": "icon", "name": "invertedBkgHeaderIconStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.IconFillColorInverted%"}, {"property": "PressedFill", "value": "%Palette.IconPressedFillColorInverted%"}, {"property": "DisabledColor", "value": "%Palette.DisabledButtonBkgColor%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}]}, {"controlTemplateName": "microphone", "name": "defaultMicrophoneStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColorInverted%"}, {"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "HoverFill", "value": "%Palette.HoverButtonBkgColor%"}, {"property": "HoverColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "DisabledColor", "value": "RGBA(186, 186, 186, 1)"}, {"property": "PressedColor", "value": "Self.Color"}, {"property": "DisabledBorderColor", "value": "RGBA(56, 56, 56, 1)"}, {"property": "PressedBorderColor", "value": "ColorFade(Self.BorderColor, -15%)"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 15%)"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "DisabledFill", "value": "RGBA(119, 119, 119, 1)"}, {"property": "PressedFill", "value": "ColorFade(Self.Fill, -15%)"}]}, {"controlTemplateName": "barcode", "name": "defaultBarcodeStyle", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"controlTemplateName": "groupContainer", "name": "defaultGroupContainerStyle", "propertyValuesMap": [{"property": "RadiusTopLeft", "value": "%Palette.ContainerRadius%"}, {"property": "RadiusBottomRight", "value": "%Palette.ContainerRadius%"}, {"property": "RadiusTopRight", "value": "%Palette.ContainerRadius%"}, {"property": "RadiusBottomLeft", "value": "%Palette.ContainerRadius%"}, {"property": "DropShadow", "value": "%DropShadow.RESERVED%.Light"}]}, {"controlTemplateName": "barcodeScanner", "name": "defaultBarcodeScannerStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColorInverted%"}, {"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "DisabledFill", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "Size", "value": "%Palette.TextEmphasisFontSize%"}, {"property": "HoverColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "HoverFill", "value": "%Palette.HoverButtonBkgColor%"}, {"property": "BorderColor", "value": "ColorFade(Self.Fill, -15%)"}, {"property": "RadiusTopLeft", "value": "10"}, {"property": "RadiusBottomRight", "value": "10"}, {"property": "RadiusTopRight", "value": "10"}, {"property": "RadiusBottomLeft", "value": "10"}, {"property": "PressedBorderColor", "value": "Self<PERSON>ll"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 20%)"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "2"}, {"property": "FocusedBorderThickness", "value": "4"}, {"property": "PressedColor", "value": "Self<PERSON>ll"}, {"property": "PressedFill", "value": "Self.Color"}, {"property": "Italic", "value": "false"}, {"property": "Underline", "value": "false"}, {"property": "Strikethrough", "value": "false"}, {"property": "Align", "value": "%Align.RESERVED%.Center"}, {"property": "VerticalAlign", "value": "%VerticalAlign.RESERVED%.Middle"}]}, {"controlTemplateName": "camera", "name": "defaultCameraStyle", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"controlTemplateName": "inkControl", "name": "defaultInkControlStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "Size", "value": "2"}, {"property": "Fill", "value": "RGBA(0, 0, 0, 0)"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "2"}]}, {"controlTemplateName": "import", "name": "defaultImportStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColorInverted%"}, {"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "DisabledFill", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "HoverFill", "value": "%Palette.HoverButtonBkgColor%"}, {"property": "HoverColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "Size", "value": "%Palette.TextEmphasisFontSize%"}, {"property": "RadiusTopLeft", "value": "10"}, {"property": "RadiusTopRight", "value": "10"}, {"property": "RadiusBottomLeft", "value": "10"}, {"property": "RadiusBottomRight", "value": "10"}, {"property": "BorderColor", "value": "ColorFade(Self.Fill, -15%)"}, {"property": "DisabledBorderColor", "value": "RGBA(56, 56, 56, 1)"}, {"property": "PressedBorderColor", "value": "ColorFade(Self.BorderColor, -20%)"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 20%)"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "2"}, {"property": "FocusedBorderThickness", "value": "4"}, {"property": "PressedColor", "value": "Self.Color"}, {"property": "PressedFill", "value": "ColorFade(Self.Fill, -20%)"}]}, {"controlTemplateName": "image", "name": "defaultImageStyle", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "DisabledFill", "value": "%Palette.DisabledButtonBkgColor%"}, {"property": "Fill", "value": "RGBA(0, 0, 0, 0)"}, {"property": "PressedFill", "value": "ColorFade(Self.Fill, -20%)"}, {"property": "HoverFill", "value": "ColorFade(Self.Fill, 20%)"}, {"property": "RadiusTopLeft", "value": "0"}, {"property": "RadiusTopRight", "value": "0"}, {"property": "RadiusBottomLeft", "value": "0"}, {"property": "RadiusBottomRight", "value": "0"}, {"property": "PressedBorderColor", "value": "ColorFade(Self.BorderColor, -20%)"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 20%)"}, {"property": "BorderThickness", "value": "0"}, {"property": "FocusedBorderThickness", "value": "2"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "PaddingTop", "value": "0"}, {"property": "PaddingRight", "value": "0"}, {"property": "PaddingBottom", "value": "0"}, {"property": "PaddingLeft", "value": "0"}]}, {"controlTemplateName": "htmlviewer", "name": "defaultHtmlViewerStyle", "propertyValuesMap": [{"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "Size", "value": "%Palette.TextBodyFontSize%"}]}, {"controlTemplateName": "htmlviewer", "name": "typedDataCardHtmlViewerStyle", "propertyValuesMap": [{"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "Size", "value": "%Palette.TextBodyFontSize%"}, {"property": "Color", "value": "%Palette.TextLinkColor%"}]}, {"controlTemplateName": "export", "name": "defaultExportStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColorInverted%"}, {"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "DisabledFill", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "HoverFill", "value": "%Palette.HoverButtonBkgColor%"}, {"property": "HoverColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "Size", "value": "%Palette.TextEmphasisFontSize%"}, {"property": "RadiusTopLeft", "value": "10"}, {"property": "RadiusTopRight", "value": "10"}, {"property": "RadiusBottomLeft", "value": "10"}, {"property": "RadiusBottomRight", "value": "10"}, {"property": "BorderColor", "value": "ColorFade(Self.Fill, -15%)"}, {"property": "DisabledBorderColor", "value": "RGBA(56, 56, 56, 1)"}, {"property": "PressedBorderColor", "value": "ColorFade(Self.BorderColor, -20%)"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 20%)"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "2"}, {"property": "FocusedBorderThickness", "value": "4"}, {"property": "PressedColor", "value": "Self.Color"}, {"property": "PressedFill", "value": "ColorFade(Self.Fill, -20%)"}]}, {"controlTemplateName": "addMedia", "name": "defaultAddMediaStyle", "propertyValuesMap": [{"property": "Color", "value": "%Palette.InputTextColor%"}, {"property": "Fill", "value": "%Palette.InputBkgColor%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "FocusedBorderThickness", "value": "2"}, {"property": "Font", "value": "%Font.RESERVED%.'Open Sans'"}, {"property": "Size", "value": "11"}, {"property": "Italic", "value": "false"}, {"property": "Underline", "value": "false"}, {"property": "Strikethrough", "value": "false"}, {"property": "DisabledBorderColor", "value": "RGBA(56, 56, 56, 1)"}, {"property": "PressedBorderColor", "value": "ColorFade(Self.BorderColor, -20%)"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 20%)"}, {"property": "DisabledColor", "value": "RGBA(186, 186, 186, 1)"}, {"property": "PressedColor", "value": "Self.Color"}, {"property": "HoverColor", "value": "Self.Color"}, {"property": "DisabledFill", "value": "RGBA(119, 119, 119, 1)"}, {"property": "PressedFill", "value": "ColorFade(Self.Fill, -20%)"}, {"property": "HoverFill", "value": "ColorFade(Self.Fill, 20%)"}, {"property": "FontWeight", "value": "%FontWeight.RESERVED%.Semibold"}, {"property": "Align", "value": "%Align.RESERVED%.Center"}, {"property": "VerticalAlign", "value": "%VerticalAlign.RESERVED%.Middle"}]}, {"controlTemplateName": "audioPlayback", "name": "defaultAudioPlaybackStyle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}]}, {"controlTemplateName": "rectangle", "name": "defaultRectangleStyle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "DisabledFill", "value": "Self<PERSON>ll"}, {"property": "PressedFill", "value": "Self<PERSON>ll"}, {"property": "HoverFill", "value": "Self<PERSON>ll"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "FocusedBorderThickness", "value": "2"}]}, {"controlTemplateName": "rectangle", "name": "primary2RectangleStyle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor2%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"controlTemplateName": "rectangle", "name": "primary3RectangleStyle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor3%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"controlTemplateName": "rectangle", "name": "primary3FadeRectangleStyle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor3Fade%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"controlTemplateName": "rectangle", "name": "grayRectangleStyle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.ReservedGrayColor%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"controlTemplateName": "rectangle", "name": "invertedBackgroundRectangleStyle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.InvertedBkgColor%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"controlTemplateName": "rectangle", "name": "overlayRectangleStyle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.OverlayBkgColor%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"controlTemplateName": "typedDataCard", "name": "defaultTypedDataCardStyle", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"controlTemplateName": "entityForm", "name": "defaultEntityFormStyle", "propertyValuesMap": [{"property": "TextColor", "value": "%Palette.TextMainColor%"}, {"property": "InputTextColor", "value": "%Palette.InputTextColor%"}, {"property": "DisabledTextColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "PrimaryColor1", "value": "%Palette.PrimaryColor1%"}, {"property": "PrimaryColor2", "value": "%Palette.PrimaryColor2%"}, {"property": "PrimaryColor3", "value": "%Palette.PrimaryColor3%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "InputBackgroundColor", "value": "%Palette.InputBkgColor%"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "Font", "value": "%Font.RESERVED%.'Open Sans'"}, {"property": "FontWeight", "value": "%FontWeight.RESERVED%.Normal"}]}, {"controlTemplateName": "dataGrid", "name": "defaultDataGridStyle", "propertyValuesMap": [{"property": "LinkColor", "value": "%Palette.TextLinkColor%"}, {"property": "PrimaryColor1", "value": "%Palette.PrimaryColor1%"}, {"property": "PrimaryColor2", "value": "%Palette.PrimaryColor2%"}, {"property": "PrimaryColor3", "value": "%Palette.PrimaryColor3%"}, {"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "InvertedColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "SelectedFill", "value": "%Palette.PrimaryColor1Light%"}, {"property": "SelectedColor", "value": "%Palette.TextMainColor%"}, {"property": "HoverFill", "value": "%Palette.PrimaryColor3Light%"}, {"property": "HoverColor", "value": "%Palette.TextMainColor%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "InputFill", "value": "%Palette.InputBkgColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "Size", "value": "%Palette.TextBodyFontSize%"}, {"property": "HeadingFont", "value": "%Palette.TextBodyFontFace%"}, {"property": "HeadingFontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "HeadingSize", "value": "%Palette.TextBodyFontSize%"}, {"property": "HeadingColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "HeadingFill", "value": "%Palette.PrimaryColor1%"}]}, {"controlTemplateName": "powerbi", "name": "defaultPowerbiStyle", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"controlTemplateName": "microsoftStreamPlayback", "name": "defaultMicrosoftStreamPlaybackStyle", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "Fill", "value": "%Palette.ScreenBkgColor%"}]}, {"controlTemplateName": "form", "name": "defaultFormStyle", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"controlTemplateName": "formViewer", "name": "defaultFormViewerStyle", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"controlTemplateName": "pdfViewer", "name": "defaultPdfViewerStyle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.ScreenBkgColor%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"controlTemplateName": "gallery", "name": "defaultGalleryStyle", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"controlTemplateName": "richTextEditor", "name": "defaultRichTextEditorStyle", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"controlTemplateName": "dataTable", "name": "defaultDataTableStyle", "propertyValuesMap": [{"property": "LinkColor", "value": "%Palette.TextLinkColor%"}, {"property": "PrimaryColor1", "value": "%Palette.PrimaryColor1%"}, {"property": "PrimaryColor2", "value": "%Palette.PrimaryColor2%"}, {"property": "PrimaryColor3", "value": "%Palette.PrimaryColor3%"}, {"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "InvertedColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "SelectedFill", "value": "%Palette.PrimaryColor1Light%"}, {"property": "SelectedColor", "value": "%Palette.TextMainColor%"}, {"property": "HoverFill", "value": "%Palette.PrimaryColor3Light%"}, {"property": "HoverColor", "value": "%Palette.TextMainColor%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "InputFill", "value": "%Palette.InputBkgColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "Size", "value": "%Palette.TextBodyFontSize%"}, {"property": "HeadingFont", "value": "%Palette.TextBodyFontFace%"}, {"property": "HeadingFontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "HeadingSize", "value": "%Palette.TextBodyFontSize%"}, {"property": "HeadingColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "HeadingFill", "value": "%Palette.PrimaryColor1%"}]}]}]}