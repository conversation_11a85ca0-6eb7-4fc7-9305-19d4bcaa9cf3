<widget xmlns="http://openajax.org/metadata" spec="1.0" id="http://microsoft.com/appmagic/gallery" name="gallery" jsClass="AppMagic.Controls.Gallery.GalleryControl" version="2.15.0" styleable="true" runtimeCost="1" xmlns:appMagic="http://schemas.microsoft.com/appMagic">
  <author name="Microsoft AppMagic" />
  <license type="text/html"><![CDATA[<p>TODO:  Need license text here.</p>]]></license>
  <description><![CDATA[GALLERY
      Control description here.]]></description>
  <requires>
    <require type="css" src="css/gallery.css" />
    <require type="javascript" src="js/gallery.js" />
    <!--
      This would cause imageGallery added with just sample data to not show up in published app.
      None of our scenarios need this for now and this is one of the 2 items that can
      be left out from publish time to demonstrate the feature. Therefore, we are marking it designOnly.
    -->
    <require type="other" src="data/imageGallery/ImageGallerySample.xlsx" authoringOnly="true" />
    <require type="other" src="data/imageGallery/Image_Placeholder.svg" authoringOnly="true" />
    <require type="other" src="data/textualGallery/TextualGallerySample.xlsx" authoringOnly="true" />
    <require type="other" src="data/CustomGallerySample.xlsx" authoringOnly="true" />
    <require type="other" src="data/CardStackGallerySample.xlsx" authoringOnly="true" />
  </requires>
  <appMagic:capabilities supportsNestedControls="true" replicatesNestedControls="true" contextualViewsEnabled="true" autoBorders="true" autoFocusedBorders="true" autoFill="true" autoDisabledViewState="true" screenActiveAware="true" replicationLimit="1" isVersionFlexible="true" />
  <appMagic:accessibilityChecks controlIsInteractive="true" />
  <appMagic:nestedWidgets>
    <appMagic:dataControlWidget name="galleryTemplate" jsClass="AppMagic.Controls.Gallery.Template" id="http://microsoft.com/appmagic/galleryTemplate" version="1.0" runtimeCost="1">
      <appMagic:capabilities intangible="true" viewContainer="true" addPropertiesToParent="true" />
      <properties>
        <!-- Data properties -->
        <property name="IsSelected" localizedName="##gallery_IsSelected##" datatype="Boolean" direction="out">
          <title>##gallery_IsSelected_Description##</title>
          <appMagic:category>data</appMagic:category>
        </property>
        <property name="ItemAccessibleLabel" localizedName="##gallery_ItemAccessibleLabel##" datatype="String" direction="in" defaultValue="">
          <title>Accessible label for a gallery item</title>
          <appMagic:category>data</appMagic:category>
          <appMagic:displayName>##gallery_ItemAccessibleLabel_DisplayName##</appMagic:displayName>
          <appMagic:tooltip>##gallery_ItemAccessibleLabel_Tooltip##</appMagic:tooltip>
        </property>
        <!-- Design properties -->
        <property name="TemplateFill" localizedName="##gallery_TemplateFill##" datatype="Color" direction="in" defaultValue="RGBA(0, 0, 0, 0)" nestedDefaultValue="RGBA(0, 0, 0, 0)" isExpr="true" converter="argbConverter">
          <title>##gallery_TemplateFill_Description##</title>
          <appMagic:tooltip>##gallery_TemplateFill_Tooltip##</appMagic:tooltip>
          <appMagic:category>design</appMagic:category>
          <appMagic:helperUI>color</appMagic:helperUI>
          <appMagic:displayName>##gallery_TemplateFill_DisplayName##</appMagic:displayName>
        </property>
        <!-- Behavior properties -->
        <property name="OnSelect" localizedName="##gallery_OnSelect##" datatype="Boolean" defaultValue="false" direction="in">
          <title>On select</title>
          <appMagic:tooltip>##gallery_OnSelect_Tooltip##</appMagic:tooltip>
          <appMagic:category>behavior</appMagic:category>
          <appMagic:displayName>##gallery_OnSelect_DisplayName##</appMagic:displayName>
        </property>
      </properties>
    </appMagic:dataControlWidget>
  </appMagic:nestedWidgets>
  <properties>
    <property name="Items" localizedName="##gallery_Items##" datatype="Array" direction="in" isPrimaryInputProperty="true" hasEditableNameMap="true" supportsPaging="true" errorCapability="recordErrors">
      <title>##gallery_Items_Description##</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:thisItemInput></appMagic:thisItemInput>
      <appMagic:displayName>##gallery_Items_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##gallery_Items_Tooltip##</appMagic:tooltip>
      <appMagic:sampleDataSource name="CustomGallerySample" location="data/CustomGallerySample.xlsx" />
    </property>
    <property name="Default" localizedName="##gallery_Default##" datatype="object" direction="in" isExpr="true" isTypeBoundToPrimaryInput="true">
      <title>##gallery_Default_Description##</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:passThroughReference>Items</appMagic:passThroughReference>
      <appMagic:displayName>##gallery_Default_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##gallery_Default_Tooltip##</appMagic:tooltip>
    </property>
    <property name="AllItems" localizedName="##gallery_AllItems##" datatype="Array" direction="out">
      <title>##gallery_AllItems_Description##</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:passThroughReference>Items</appMagic:passThroughReference>
      <appMagic:nestedAware />
    </property>
    <property name="AllItemsCount" localizedName="##gallery_AllItemsCount##" datatype="Number" direction="out">
      <title>##gallery_AllItemsCount_Description##</title>
      <appMagic:category>data</appMagic:category>
    </property>
    <property name="WrapCount" localizedName="##gallery_WrapCount##" datatype="Number" defaultValue="1" minimum="1" maximum="10" direction="in" hidden="false">
      <title>##gallery_WrapCount_Description##</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##gallery_WrapCount_DisplayName##</appMagic:displayName>
    </property>
    <property name="Selectable" localizedName="##gallery_Selectable##" datatype="Boolean" defaultValue="true" direction="in">
      <title>Selectable</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##gallery_Selectable_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##gallery_Selectable_Tooltip##</appMagic:tooltip>
    </property>
    <property name="Selected" localizedName="##gallery_Selected##" datatype="object" direction="out" isPrimaryOutputProperty="true" indicatesActiveItem="true" supportsAutomation="true">
      <title>##gallery_Selected_Description##</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:passThroughReference>Items</appMagic:passThroughReference>
      <appMagic:nestedAware />
    </property>
    <property name="VisibleIndex" localizedName="##gallery_VisibleIndex##" datatype="Number" direction="out" defaultValue="1">
      <title>##gallery_VisibleIndex_Description##</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##gallery_VisibleIndex_DisplayName##</appMagic:displayName>
    </property>
    <property name="TemplateSize" localizedName="##gallery_TemplateSize##" datatype="Number" direction="in" defaultValue="320" minimum="1">
      <title>##gallery_TemplateSize_Description##</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>templateSize</appMagic:helperUI>
      <appMagic:displayName>##gallery_TemplateSize_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##gallery_TemplateSize_Tooltip##</appMagic:tooltip>
    </property>
    <property name="TemplateWidth" localizedName="##gallery_TemplateWidth##" datatype="Number" direction="out" modelValueConstrainer="templateWidthConstrainer" defaultValue="320">
      <title>##gallery_TemplateWidth_Description##</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##gallery_TemplateWidth_DisplayName##</appMagic:displayName>
    </property>
    <property name="TemplateMaximumWidth" localizedName="##gallery_TemplateMaximumWidth##" datatype="Number" defaultValue="0" hidden="true">
      <title>##gallery_TemplateMaximumWidth_Description##</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##gallery_TemplateMaximumWidth_DisplayName##</appMagic:displayName>
    </property>
    <property name="HorizontalScrollPosition" localizedName="##gallery_HorizontalScrollPosition##" datatype="Number" direction="out" defaultValue="0" hidden="true">
      <title>##gallery_HorizontalScrollPosition_Description##</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##gallery_HorizontalScrollPosition_DisplayName##</appMagic:displayName>
    </property>
    <property name="TemplateHeight" localizedName="##gallery_TemplateHeight##" datatype="Number" direction="out" modelValueConstrainer="templateHeightConstrainer" defaultValue="320">
      <title>##gallery_TemplateHeight_Description##</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##gallery_TemplateHeight_DisplayName##</appMagic:displayName>
    </property>
    <property name="TemplatePadding" localizedName="##gallery_TemplatePadding##" datatype="Number" defaultValue="5">
      <title>##gallery_TemplatePadding_Description##</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##gallery_TemplatePadding_DisplayName##</appMagic:displayName>
      <appMagic:helperUI>templatePadding</appMagic:helperUI>
      <appMagic:tooltip>##gallery_TemplatePadding_Tooltip##</appMagic:tooltip>
    </property>
    <property name="MaxTemplateSize" localizedName="##gallery_MaxTemplateSize##" datatype="Number" defaultValue="5000" hidden="true">
      <title>##gallery_MaxTemplateSize_Description##</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##gallery_MaxTemplateSize_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##gallery_MaxTemplateSize_Tooltip##</appMagic:tooltip>
    </property>
    <property name="Layout" localizedName="##gallery_Layout##" datatype="Layout" defaultValue="%Layout.RESERVED%.Horizontal" isExpr="true" hidden="true">
      <title>##gallery_Layout_Description##</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>layout</appMagic:helperUI>
      <appMagic:displayName>##gallery_Layout_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##gallery_Layout_Tooltip##</appMagic:tooltip>
    </property>
    <property name="Transition" localizedName="##gallery_Transition##" datatype="Transition" direction="in" defaultValue="%Transition.RESERVED%.None" isExpr="true">
      <title>##gallery_Transition_Description##</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>transition</appMagic:helperUI>
      <appMagic:displayName>##gallery_Transition_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##gallery_Transition_Tooltip##</appMagic:tooltip>
    </property>
    <property name="ShowNavigation" localizedName="##gallery_ShowNavigation##" datatype="Boolean" direction="in" defaultValue="false">
      <title>##gallery_ShowNavigation_Description##</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>boolean</appMagic:helperUI>
      <appMagic:displayName>##gallery_ShowNavigation_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##gallery_ShowNavigation_Tooltip##</appMagic:tooltip>
    </property>
    <property name="NavigationStep" localizedName="##gallery_NavigationStep##" datatype="Number" direction="in" defaultValue="1" minimum="1">
      <title>##gallery_NavigationStep_Description##</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##gallery_NavigationStep_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##gallery_NavigationStep_Tooltip##</appMagic:tooltip>
    </property>
    <property name="ShowScrollbar" localizedName="##gallery_ShowScrollbar##" datatype="boolean" direction="in" defaultValue="true">
      <title>ShowScrollbar</title>
      <appMagic:tooltip>##gallery_ShowScrollbar_Tooltip##</appMagic:tooltip>
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>boolean</appMagic:helperUI>
      <appMagic:displayName>##gallery_ShowScrollbar_DisplayName##</appMagic:displayName>
    </property>
    <property name="Valid" localizedName="##commonProperties_Valid##" datatype="Boolean" direction="out">
      <title>Valid</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##commonProperties_Valid_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##commonProperties_Valid_Tooltip##</appMagic:tooltip>
    </property>
    <!-- SelectionTracksMove seems unused. 89fec6555 should be reverted when KO gallery is obsolete -->
    <property name="SelectionTracksMove" localizedName="##gallery_SelectionTracksMove##" datatype="boolean" direction="in" defaultValue="false" hidden="true">
      <title>SelectionTracksMove</title>
      <appMagic:category>design</appMagic:category>
    </property>
    <property name="Reset" localizedName="##commonProperties_Reset##" datatype="Boolean" defaultValue="false" direction="in" hidden="true">
      <title>Reset</title>
      <appMagic:category>data</appMagic:category>
      <appMagic:displayName>##commonProperties_Reset_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##commonProperties_Reset_Tooltip##</appMagic:tooltip>
    </property>
    <!-- This is for allowing the gallery to grow and shrink vertically to display the content -->
    <property name="AutoHeight" localizedName="##CommonProperties_AutoHeight##" datatype="Boolean" defaultValue="false" hidden="true">
      <title>AutoHeight</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##CommonProperties_AutoHeight_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##CommonProperties_AutoHeight_Tooltip##</appMagic:tooltip>
    </property>
    <property name="DelayItemLoading" localizedName="##gallery_DelayItemLoading##" datatype="Boolean" direction="in" defaultValue="false">
      <title>DelayItemLoading</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>boolean</appMagic:helperUI>
      <appMagic:displayName>##gallery_DelayItemLoading_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##gallery_DelayItemLoading_Tooltip##</appMagic:tooltip>
    </property>
    <property name="LoadingSpinner" localizedName="##CommonProperties_LoadingSpinner##" datatype="LoadingSpinner" direction="in" defaultValue="%LoadingSpinner.RESERVED%.None" isExpr="true">
      <title>LoadingSpinner</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:displayName>##CommonProperties_LoadingSpinner_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##CommonProperties_LoadingSpinner_Tooltip##</appMagic:tooltip>
    </property>
    <property name="LoadingSpinnerColor" localizedName="##CommonProperties_LoadingSpinnerColor##" datatype="Color" direction="in" defaultValue="Self.BorderColor" isExpr="true">
      <title>LoadingSpinner</title>
      <appMagic:category>design</appMagic:category>
      <appMagic:helperUI>color</appMagic:helperUI>
      <appMagic:displayName>##CommonProperties_LoadingSpinnerColor_DisplayName##</appMagic:displayName>
      <appMagic:tooltip>##CommonProperties_LoadingSpinnerColor_Tooltip##</appMagic:tooltip>
    </property>
  </properties>
  <appMagic:includeProperties>
    <!-- Data -->
    <appMagic:includeProperty name="AccessibleLabel" />
    <appMagic:includeProperty name="ContentLanguage" />
    <!-- Design -->
    <appMagic:includeProperty name="DisplayMode" />
    <appMagic:includeProperty name="BorderColor" />
    <appMagic:includeProperty name="DisabledBorderColor" defaultValue="Self.BorderColor" hidden="true" />
    <appMagic:includeProperty name="PressedBorderColor" defaultValue="Self.BorderColor" hidden="true" />
    <appMagic:includeProperty name="HoverBorderColor" defaultValue="Self.BorderColor" hidden="true" />
    <appMagic:includeProperty name="BorderThickness" />
    <appMagic:includeProperty name="BorderStyle" />
    <appMagic:includeProperty name="FocusedBorderColor" defaultValue="Self.BorderColor" isExpr="true" />
    <appMagic:includeProperty name="FocusedBorderThickness" defaultValue="4" />
    <appMagic:includeProperty name="Fill" />
    <appMagic:includeProperty name="DisabledFill" defaultValue="Self.Fill" hidden="true" />
    <appMagic:includeProperty name="PressedFill" defaultValue="Self.Fill" hidden="true" />
    <appMagic:includeProperty name="HoverFill" defaultValue="Self.Fill" hidden="true" />
    <appMagic:includeProperty name="X" />
    <appMagic:includeProperty name="Y" />
    <appMagic:includeProperty name="Width" defaultValue="640" />
    <appMagic:includeProperty name="Height" defaultValue="575" />
    <appMagic:includeProperty name="Visible" />
    <appMagic:includeProperty name="TabIndex" defaultValue="-1" />
    <!-- Hidden properties -->
    <appMagic:includeProperty name="minimumWidth" defaultValue="20" />
    <appMagic:includeProperty name="minimumHeight" defaultValue="20" />
    <appMagic:includeProperty name="maximumWidth" defaultValue="1366" />
    <appMagic:includeProperty name="maximumHeight" defaultValue="768" />
    <appMagic:includeProperty name="#CopilotOverlayLabel" defaultValue="##gallery_CopilotOverlayLabel##" hidden="true" />
    <appMagic:includeProperty name="#CopilotOverlayVisible" defaultValue="false" hidden="true" />
  </appMagic:includeProperties>
  <!-- Display metadata providing property visibility, order, sections, and grouping in UI (e.g. properties panel) -->
  <appMagic:displayMetadata>
    <appMagic:section>
      <appMagic:property name="Items" serverProvidesValue="true" computedValueType="NativeCdsDataSourceName" shouldAutoBind="true" dependentProperties="ControlLayout" labelOverride="##ControlSidebar_PropertiesPanel_Datasource_Displayname##" showInCommandBar="true" showInCanvasInlineActionBar="true" />
      <appMagic:dataSourceSelectionCallout dataSourcePropertyName="Items" />
      <appMagic:configureCdsViews propertyToReplace="Items" />
      <appMagic:wizardPropertyGroup>
        <appMagic:wizardStep label="##ControlSidebar_PropertiesPanel_Fields_DisplayName##" panelTitle="##ControlSidebar_PropertiesPanel_Data_DisplayName##" linkedLocation="GalleryItemsPane" propertyInvariantName="Fields" computedValueType="FieldSummaryEdit" serverProvidesValue="true" showInCommandBar="true" showInCanvasInlineActionBar="true" />
      </appMagic:wizardPropertyGroup>
    </appMagic:section>
    <appMagic:section>
      <appMagic:serverProperty name="ControlLayout" label="##ControlSidebar_PropertiesPanel_Layout_DisplayName##" serverProvidesValue="true" showInCommandBar="true" computedValueType="ControlLayout" propertyInvariantName="ControlLayout" showInCanvasInlineActionBar="true" />
    </appMagic:section>
    <appMagic:section>
      <appMagic:property name="Visible" />
      <appMagic:propertyGroup name="Position">
        <appMagic:property name="X" />
        <appMagic:property name="Y" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="Size">
        <appMagic:property name="Width" />
        <appMagic:property name="Height" />
      </appMagic:propertyGroup>
      <appMagic:property name="AutoHeight" />
    </appMagic:section>
    <appMagic:section>
      <appMagic:propertyGroup name="Color">
        <appMagic:property name="Fill" showInFloatie="true" showInCommandBar="true" />
      </appMagic:propertyGroup>
      <appMagic:propertyGroup name="Border">
        <appMagic:property name="BorderStyle" />
        <appMagic:property name="BorderThickness" />
        <appMagic:property name="BorderColor" />
      </appMagic:propertyGroup>
    </appMagic:section>
    <appMagic:section>
      <appMagic:property name="WrapCount" />
      <appMagic:property name="TemplateSize" />
      <appMagic:property name="TemplatePadding" />
    </appMagic:section>
    <appMagic:section>
      <appMagic:property name="ShowScrollbar" />
      <appMagic:property name="ShowNavigation" />
      <appMagic:property name="NavigationStep" />
      <appMagic:property name="Transition" />
      <appMagic:property name="DisplayMode" />
      <appMagic:property name="TabIndex" />
    </appMagic:section>
  </appMagic:displayMetadata>
  <appMagic:controlVariants>
    <appMagic:controlVariant name="galleryHorizontal" localizedName="##Gallery_Variant_Blank##" supportedOrientation="horizontal" layoutEnabled="true" layoutGroupId="carousel">
      <appMagic:insertMetadata isDeviceOptimized="true">
        <appMagic:category name="Layout" priority="50" />
      </appMagic:insertMetadata>
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="TemplateSize" defaultValue="If(Self.Layout = %Layout.RESERVED%.Horizontal, Min(320, Self.Width - 60), Min(320, Self.Height - 60))" isExpr="true" />
        <appMagic:overrideProperty name="TemplatePadding" defaultValue="5" />
        <appMagic:overrideProperty name="Layout" defaultValue="%Layout.RESERVED%.Horizontal" isExpr="true" />
        <appMagic:overrideProperty name="DelayItemLoading" defaultValue="true" />
        <appMagic:overrideProperty name="LoadingSpinner" defaultValue="%LoadingSpinner.RESERVED%.Data" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="galleryVertical" localizedName="##Gallery_Variant_Blank##" supportedOrientation="vertical" layoutEnabled="true" layoutGroupId="list">
      <appMagic:insertMetadata isDeviceOptimized="true">
        <appMagic:category name="Layout" priority="40" />
      </appMagic:insertMetadata>
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="TemplateSize" defaultValue="If(Self.Layout = %Layout.RESERVED%.Horizontal, Min(280, Self.Width - 60), Min(280, Self.Height - 60))" isExpr="true" />
        <appMagic:overrideProperty name="TemplatePadding" defaultValue="5" />
        <appMagic:overrideProperty name="Layout" defaultValue="%Layout.RESERVED%.Vertical" isExpr="true" />
        <appMagic:overrideProperty name="WrapCount" defaultValue="1" />
        <appMagic:overrideProperty name="DelayItemLoading" defaultValue="true" />
        <appMagic:overrideProperty name="LoadingSpinner" defaultValue="%LoadingSpinner.RESERVED%.Data" isExpr="true" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="variableTemplateHeightGallery" localizedName="##Gallery_Variant_Blank##" supportedOrientation="flexibleVertical" layoutEnabled="true" layoutGroupId="flexibleHeight">
      <appMagic:insertMetadata isDeviceOptimized="true">
        <appMagic:category name="Layout" priority="60" />
      </appMagic:insertMetadata>
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="TemplateSize" defaultValue="280" />
        <appMagic:overrideProperty name="Layout" defaultValue="%Layout.RESERVED%.Vertical" isExpr="true" hidden="true" />
        <appMagic:overrideProperty name="WrapCount" defaultValue="1" hidden="true" />
        <appMagic:overrideProperty name="DelayItemLoading" defaultValue="true" />
        <appMagic:overrideProperty name="LoadingSpinner" defaultValue="%LoadingSpinner.RESERVED%.Data" isExpr="true" />
        <appMagic:overrideProperty name="MaxTemplateSize" defaultValue="5000" hidden="false" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="printableGallery" localizedName="##Gallery_Variant_Blank##" supportedOrientation="vertical" layoutEnabled="false">
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="TemplateSize" defaultValue="150" />
        <appMagic:overrideProperty name="Layout" defaultValue="%Layout.RESERVED%.Vertical" isExpr="true" hidden="true" />
        <appMagic:overrideProperty name="WrapCount" defaultValue="1" hidden="true" />
        <appMagic:overrideProperty name="AutoHeight" defaultValue="true" hidden="false" />
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="textualGalleryHorizontal" supportedOrientation="horizontal">
      <appMagic:controls>
        <appMagic:control template="Label" localizedName="##variantControl_Heading##">
          <appMagic:rules>
            <appMagic:rule name="Text" value="Heading" isExpr="true" />
            <appMagic:rule name="Wrap" value="false" />
            <appMagic:rule name="X" value="0" />
            <appMagic:rule name="Y" value="0" />
            <appMagic:rule name="Width" value="%PARENTCONTROL.ID%.TemplateWidth" isExpr="true" />
            <appMagic:rule name="Height" value="44" />
            <appMagic:rule name="PaddingTop" value="4" />
            <appMagic:rule name="PaddingBottom" value="4" />
          </appMagic:rules>
        </appMagic:control>
        <appMagic:control template="Label" localizedName="##variantControl_Subtitle##" defaultStyle="subtitleLabelStyle">
          <appMagic:rules>
            <appMagic:rule name="Text" value="Subtitle" isExpr="true" />
            <appMagic:rule name="Wrap" value="false" />
            <appMagic:rule name="X" value="0" />
            <appMagic:rule name="Y" value="44" />
            <appMagic:rule name="Width" value="%PARENTCONTROL.ID%.TemplateWidth" isExpr="true" />
            <appMagic:rule name="Height" value="24" />
            <appMagic:rule name="PaddingTop" value="0" />
            <appMagic:rule name="PaddingBottom" value="0" />
          </appMagic:rules>
        </appMagic:control>
        <appMagic:control template="Label" localizedName="##variantControl_Body##" defaultStyle="contentLabelStyle">
          <appMagic:rules>
            <appMagic:rule name="Text" value="Body" isExpr="true" />
            <appMagic:rule name="X" value="0" />
            <appMagic:rule name="Y" value="68" />
            <appMagic:rule name="Width" value="%PARENTCONTROL.ID%.TemplateWidth" isExpr="true" />
            <appMagic:rule name="Height" value="%PARENTCONTROL.ID%.TemplateHeight - 68" isExpr="true" />
          </appMagic:rules>
        </appMagic:control>
      </appMagic:controls>
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="TemplateSize" defaultValue="If(Self.Layout = %Layout.RESERVED%.Horizontal, Min(380, Self.Width - 60), Min(120, Self.Height - 60))" isExpr="true" />
        <appMagic:overrideProperty name="TemplatePadding" defaultValue="5" />
        <appMagic:overrideProperty name="Items">
          <appMagic:sampleDataSource name="TextualGallerySample" location="data/textualGallery/TextualGallerySample.xlsx" />
        </appMagic:overrideProperty>
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="textualGalleryVertical" supportedOrientation="vertical">
      <appMagic:controls>
        <appMagic:control template="Label" localizedName="##variantControl_Heading##">
          <appMagic:rules>
            <appMagic:rule name="Text" value="Heading" isExpr="true" />
            <appMagic:rule name="Wrap" value="false" />
            <appMagic:rule name="X" value="0" />
            <appMagic:rule name="Y" value="0" />
            <appMagic:rule name="Width" value="%PARENTCONTROL.ID%.TemplateWidth" isExpr="true" />
            <appMagic:rule name="Height" value="44" />
            <appMagic:rule name="PaddingTop" value="4" />
            <appMagic:rule name="PaddingBottom" value="4" />
          </appMagic:rules>
        </appMagic:control>
        <appMagic:control template="Label" localizedName="##variantControl_Subtitle##" defaultStyle="subtitleLabelStyle">
          <appMagic:rules>
            <appMagic:rule name="Text" value="Subtitle" isExpr="true" />
            <appMagic:rule name="Wrap" value="false" />
            <appMagic:rule name="X" value="0" />
            <appMagic:rule name="Y" value="44" />
            <appMagic:rule name="Width" value="%PARENTCONTROL.ID%.TemplateWidth" isExpr="true" />
            <appMagic:rule name="Height" value="24" />
            <appMagic:rule name="PaddingTop" value="0" />
            <appMagic:rule name="PaddingBottom" value="0" />
          </appMagic:rules>
        </appMagic:control>
        <appMagic:control template="Label" localizedName="##variantControl_Body##" defaultStyle="contentLabelStyle">
          <appMagic:rules>
            <appMagic:rule name="Text" value="Body" isExpr="true" />
            <appMagic:rule name="X" value="0" />
            <appMagic:rule name="Y" value="68" />
            <appMagic:rule name="Width" value="%PARENTCONTROL.ID%.TemplateWidth" isExpr="true" />
            <appMagic:rule name="Height" value="%PARENTCONTROL.ID%.TemplateHeight - 68" isExpr="true" />
          </appMagic:rules>
        </appMagic:control>
      </appMagic:controls>
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Layout" defaultValue="%Layout.RESERVED%.Vertical" isExpr="true" />
        <appMagic:overrideProperty name="TemplateSize" defaultValue="If(Self.Layout = %Layout.RESERVED%.Horizontal, Min(380, Self.Width - 60), Min(160, Self.Height - 60))" isExpr="true" />
        <appMagic:overrideProperty name="TemplatePadding" defaultValue="5" />
        <appMagic:overrideProperty name="Items">
          <appMagic:sampleDataSource name="TextualGallerySample" location="data/textualGallery/TextualGallerySample.xlsx" />
        </appMagic:overrideProperty>
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <!-- TASK 93096 - Custom Gallery: Change ImageGallery variant name to match the control name (upper-case). -->
    <appMagic:controlVariant name="imageGalleryHorizontal" supportedOrientation="horizontal">
      <appMagic:controls>
        <appMagic:control template="Image" localizedName="##variantControl_Image##">
          <appMagic:rules>
            <appMagic:rule name="Image" value="ThisItem.Image" isExpr="true" />
            <appMagic:rule name="ImagePosition" value="%ImagePosition.RESERVED%.Fill" isExpr="true" />
            <appMagic:rule name="X" value="0" />
            <appMagic:rule name="Y" value="0" />
            <appMagic:rule name="Width" value="%PARENTCONTROL.ID%.TemplateWidth" isExpr="true" />
            <appMagic:rule name="Height" value="%PARENTCONTROL.ID%.TemplateHeight " isExpr="true" />
          </appMagic:rules>
        </appMagic:control>
      </appMagic:controls>
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="TemplateSize" defaultValue="If(Self.Layout = %Layout.RESERVED%.Horizontal, Min(Self.Height, Self.Width - 60), Min(Self.Width, Self.Height - 60))" isExpr="true" />
        <appMagic:overrideProperty name="TemplatePadding" defaultValue="5" />
        <appMagic:overrideProperty name="Items">
          <appMagic:sampleDataSource name="ImageGallerySample" location="data/ImageGallery/ImageGallerySample.xlsx" />
        </appMagic:overrideProperty>
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="imageGalleryVertical" supportedOrientation="vertical">
      <appMagic:controls>
        <appMagic:control template="Image" localizedName="##variantControl_Image##">
          <appMagic:rules>
            <appMagic:rule name="Image" value="ThisItem.Image" isExpr="true" />
            <appMagic:rule name="ImagePosition" value="%ImagePosition.RESERVED%.Fill" isExpr="true" />
            <appMagic:rule name="X" value="0" />
            <appMagic:rule name="Y" value="0" />
            <appMagic:rule name="Width" value="%PARENTCONTROL.ID%.TemplateWidth" isExpr="true" />
            <appMagic:rule name="Height" value="%PARENTCONTROL.ID%.TemplateHeight" isExpr="true" />
          </appMagic:rules>
        </appMagic:control>
      </appMagic:controls>
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Width" defaultValue="344" />
        <appMagic:overrideProperty name="Height" defaultValue="643" />
        <appMagic:overrideProperty name="Layout" defaultValue="%Layout.RESERVED%.Vertical" isExpr="true" />
        <appMagic:overrideProperty name="TemplateSize" defaultValue="If(Self.Layout = %Layout.RESERVED%.Horizontal, Min(Self.Height, Self.Width - 60), Min(Self.Width, Self.Height - 60))" isExpr="true" />
        <appMagic:overrideProperty name="TemplatePadding" defaultValue="5" />
        <appMagic:overrideProperty name="Items">
          <appMagic:sampleDataSource name="ImageGallerySample" location="data/ImageGallery/ImageGallerySample.xlsx" />
        </appMagic:overrideProperty>
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="imageGalleryWithCaptionHorizontal" supportedOrientation="horizontal">
      <appMagic:controls>
        <appMagic:control template="Image" localizedName="##variantControl_Image##">
          <appMagic:rules>
            <appMagic:rule name="Image" value="ThisItem.Image" isExpr="true" />
            <appMagic:rule name="ImagePosition" value="%ImagePosition.RESERVED%.Fill" isExpr="true" />
            <appMagic:rule name="X" value="0" />
            <appMagic:rule name="Y" value="0" />
            <appMagic:rule name="Width" value="%PARENTCONTROL.ID%.TemplateWidth" isExpr="true" />
            <appMagic:rule name="Height" value="%PARENTCONTROL.ID%.TemplateHeight" isExpr="true" />
          </appMagic:rules>
        </appMagic:control>
        <appMagic:control template="Label" localizedName="##variantControl_Title##" defaultStyle="basicNoSizeInvertedBkgLabelStyle">
          <appMagic:rules>
            <appMagic:rule name="Text" value="Title" isExpr="true" />
            <appMagic:rule name="Size" value="%PARENTCONTROL.ID%.TemplateHeight / 15" isExpr="true" />
            <appMagic:rule name="Align" value="%Align.RESERVED%.Center" isExpr="true" />
            <appMagic:rule name="Fill" value="RGBA(0, 0, 0, 0.8)" isExpr="true" />
            <appMagic:rule name="X" value="0" />
            <appMagic:rule name="Y" value="%PARENTCONTROL.ID%.TemplateHeight - Self.Height" isExpr="true" />
            <appMagic:rule name="Width" value="%PARENTCONTROL.ID%.TemplateWidth" isExpr="true" />
            <appMagic:rule name="Height" value="%PARENTCONTROL.ID%.TemplateHeight/4" isExpr="true" />
            <appMagic:rule name="VerticalAlign" value="%VerticalAlign.RESERVED%.Middle" isExpr="true" />
            <appMagic:rule name="PaddingTop" value="0" />
            <appMagic:rule name="PaddingBottom" value="0" />
          </appMagic:rules>
        </appMagic:control>
      </appMagic:controls>
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="TemplateSize" defaultValue="If(Self.Layout = %Layout.RESERVED%.Horizontal, Min(Self.Height, Self.Width - 60), Min(Self.Width, Self.Height - 60))" isExpr="true" />
        <appMagic:overrideProperty name="TemplatePadding" defaultValue="5" />
        <appMagic:overrideProperty name="Items">
          <appMagic:sampleDataSource name="ImageGallerySample" location="data/ImageGallery/ImageGallerySample.xlsx" />
        </appMagic:overrideProperty>
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="imageGalleryWithCaptionVertical" supportedOrientation="vertical">
      <appMagic:controls>
        <appMagic:control template="Image" localizedName="##variantControl_Image##">
          <appMagic:rules>
            <appMagic:rule name="Image" value="ThisItem.Image" isExpr="true" />
            <appMagic:rule name="ImagePosition" value="%ImagePosition.RESERVED%.Fill" isExpr="true" />
            <appMagic:rule name="X" value="0" />
            <appMagic:rule name="Y" value="0" />
            <appMagic:rule name="Width" value="%PARENTCONTROL.ID%.TemplateWidth" isExpr="true" />
            <appMagic:rule name="Height" value="%PARENTCONTROL.ID%.TemplateHeight" isExpr="true" />
          </appMagic:rules>
        </appMagic:control>
        <appMagic:control template="Label" localizedName="##variantControl_Title##" defaultStyle="basicNoSizeInvertedBkgLabelStyle">
          <appMagic:rules>
            <appMagic:rule name="Text" value="Title" isExpr="true" />
            <appMagic:rule name="Size" value="%PARENTCONTROL.ID%.TemplateHeight / 15" isExpr="true" />
            <appMagic:rule name="Align" value="%Align.RESERVED%.Center" isExpr="true" />
            <appMagic:rule name="Fill" value="RGBA(0, 0, 0, 0.8)" isExpr="true" />
            <appMagic:rule name="X" value="0" />
            <appMagic:rule name="Y" value="%PARENTCONTROL.ID%.TemplateHeight - Self.Height" isExpr="true" />
            <appMagic:rule name="Width" value="%PARENTCONTROL.ID%.TemplateWidth" isExpr="true" />
            <appMagic:rule name="Height" value="%PARENTCONTROL.ID%.TemplateHeight/4" isExpr="true" />
            <appMagic:rule name="VerticalAlign" value="%VerticalAlign.RESERVED%.Middle" isExpr="true" />
            <appMagic:rule name="PaddingTop" value="0" />
            <appMagic:rule name="PaddingBottom" value="0" />
          </appMagic:rules>
        </appMagic:control>
      </appMagic:controls>
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Width" defaultValue="344" />
        <appMagic:overrideProperty name="Height" defaultValue="643" />
        <appMagic:overrideProperty name="Layout" defaultValue="%Layout.RESERVED%.Vertical" isExpr="true" />
        <appMagic:overrideProperty name="TemplateSize" defaultValue="If(Self.Layout = %Layout.RESERVED%.Horizontal, Min(Self.Height, Self.Width - 60), Min(Self.Width, Self.Height - 60))" isExpr="true" />
        <appMagic:overrideProperty name="TemplatePadding" defaultValue="5" />
        <appMagic:overrideProperty name="Items">
          <appMagic:sampleDataSource name="ImageGallerySample" location="data/ImageGallery/ImageGallerySample.xlsx" />
        </appMagic:overrideProperty>
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="imageGalleryWithTextHorizontal" supportedOrientation="horizontal">
      <appMagic:controls>
        <appMagic:control template="Image" localizedName="##variantControl_Image##">
          <appMagic:rules>
            <appMagic:rule name="Image" value="ThisItem.Image" isExpr="true" />
            <appMagic:rule name="ImagePosition" value="%ImagePosition.RESERVED%.Fill" isExpr="true" />
            <appMagic:rule name="X" value="0" />
            <appMagic:rule name="Y" value="0" />
            <appMagic:rule name="Width" value="%PARENTCONTROL.ID%.TemplateWidth" isExpr="true" />
            <appMagic:rule name="Height" value="%PARENTCONTROL.ID%.TemplateHeight" isExpr="true" />
          </appMagic:rules>
        </appMagic:control>
        <appMagic:control template="Label" localizedName="##variantControl_Title##" defaultStyle="basicNoSizeInvertedBkgLabelStyle">
          <appMagic:rules>
            <appMagic:rule name="Text" value="Title" isExpr="true" />
            <appMagic:rule name="Size" value="%PARENTCONTROL.ID%.TemplateHeight/20" isExpr="true" />
            <appMagic:rule name="Align" value="%Align.RESERVED%.Center" isExpr="true" />
            <appMagic:rule name="Fill" value="RGBA(0, 0, 0, 0.8)" isExpr="true" />
            <appMagic:rule name="X" value="0" />
            <appMagic:rule name="Y" value="%PARENTCONTROL.ID%.TemplateHeight - %PARENTCONTROL.ID%.TemplateHeight/9 - Self.Height - 1" isExpr="true" />
            <appMagic:rule name="Width" value="%PARENTCONTROL.ID%.TemplateWidth" isExpr="true" />
            <appMagic:rule name="Height" value="%PARENTCONTROL.ID%.TemplateHeight/6" isExpr="true" />
            <appMagic:rule name="VerticalAlign" value="%VerticalAlign.RESERVED%.Middle" isExpr="true" />
            <appMagic:rule name="PaddingTop" value="0" />
            <appMagic:rule name="PaddingBottom" value="0" />
          </appMagic:rules>
        </appMagic:control>
        <appMagic:control template="Label" localizedName="##variantControl_Subtitle##" defaultStyle="basicNoSizeInvertedBkgLabelStyle">
          <appMagic:rules>
            <appMagic:rule name="Text" value="Subtitle" isExpr="true" />
            <appMagic:rule name="Size" value="%PARENTCONTROL.ID%.TemplateHeight/30" isExpr="true" />
            <appMagic:rule name="Align" value="%Align.RESERVED%.Center" isExpr="true" />
            <appMagic:rule name="Fill" value="RGBA(0, 0, 0, 0.8)" isExpr="true" />
            <appMagic:rule name="X" value="0" />
            <appMagic:rule name="Y" value="%PARENTCONTROL.ID%.TemplateHeight - Self.Height" isExpr="true" />
            <appMagic:rule name="Width" value="%PARENTCONTROL.ID%.TemplateWidth" isExpr="true" />
            <appMagic:rule name="Height" value="%PARENTCONTROL.ID%.TemplateHeight/9" isExpr="true" />
            <appMagic:rule name="VerticalAlign" value="%VerticalAlign.RESERVED%.Middle" isExpr="true" />
            <appMagic:rule name="PaddingTop" value="0" />
            <appMagic:rule name="PaddingBottom" value="0" />
          </appMagic:rules>
        </appMagic:control>
      </appMagic:controls>
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="TemplateSize" defaultValue="If(Self.Layout = %Layout.RESERVED%.Horizontal, Min(Self.Height, Self.Width - 60), Min(Self.Width, Self.Height - 60))" isExpr="true" />
        <appMagic:overrideProperty name="TemplatePadding" defaultValue="5" />
        <appMagic:overrideProperty name="Items">
          <appMagic:sampleDataSource name="ImageGallerySample" location="data/ImageGallery/ImageGallerySample.xlsx" />
        </appMagic:overrideProperty>
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="imageGalleryWithTextVertical" supportedOrientation="vertical">
      <appMagic:controls>
        <appMagic:control template="Image" localizedName="##variantControl_Image##">
          <appMagic:rules>
            <appMagic:rule name="Image" value="ThisItem.Image" isExpr="true" />
            <appMagic:rule name="ImagePosition" value="%ImagePosition.RESERVED%.Fill" isExpr="true" />
            <appMagic:rule name="X" value="0" />
            <appMagic:rule name="Y" value="0" />
            <appMagic:rule name="Width" value="%PARENTCONTROL.ID%.TemplateWidth" isExpr="true" />
            <appMagic:rule name="Height" value="%PARENTCONTROL.ID%.TemplateHeight" isExpr="true" />
          </appMagic:rules>
        </appMagic:control>
        <appMagic:control template="Label" localizedName="##variantControl_Title##" defaultStyle="basicNoSizeInvertedBkgLabelStyle">
          <appMagic:rules>
            <appMagic:rule name="Text" value="Title" isExpr="true" />
            <appMagic:rule name="Size" value="%PARENTCONTROL.ID%.TemplateHeight/20" isExpr="true" />
            <appMagic:rule name="Align" value="%Align.RESERVED%.Center" isExpr="true" />
            <appMagic:rule name="Fill" value="RGBA(0, 0, 0, 0.8)" isExpr="true" />
            <appMagic:rule name="X" value="0" />
            <appMagic:rule name="Y" value="%PARENTCONTROL.ID%.TemplateHeight - %PARENTCONTROL.ID%.TemplateHeight/9 - Self.Height - 1" isExpr="true" />
            <appMagic:rule name="Width" value="%PARENTCONTROL.ID%.TemplateWidth" isExpr="true" />
            <appMagic:rule name="Height" value="%PARENTCONTROL.ID%.TemplateHeight/6" isExpr="true" />
            <appMagic:rule name="VerticalAlign" value="%VerticalAlign.RESERVED%.Middle" isExpr="true" />
            <appMagic:rule name="PaddingTop" value="0" />
            <appMagic:rule name="PaddingBottom" value="0" />
          </appMagic:rules>
        </appMagic:control>
        <appMagic:control template="Label" localizedName="##variantControl_Subtitle##" defaultStyle="basicNoSizeInvertedBkgLabelStyle">
          <appMagic:rules>
            <appMagic:rule name="Text" value="Subtitle" isExpr="true" />
            <appMagic:rule name="Size" value="%PARENTCONTROL.ID%.TemplateHeight/30" isExpr="true" />
            <appMagic:rule name="Align" value="%Align.RESERVED%.Center" isExpr="true" />
            <appMagic:rule name="Fill" value="RGBA(0, 0, 0, 0.8)" isExpr="true" />
            <appMagic:rule name="X" value="0" />
            <appMagic:rule name="Y" value="%PARENTCONTROL.ID%.TemplateHeight - Self.Height" isExpr="true" />
            <appMagic:rule name="Width" value="%PARENTCONTROL.ID%.TemplateWidth" isExpr="true" />
            <appMagic:rule name="Height" value="%PARENTCONTROL.ID%.TemplateHeight/9" isExpr="true" />
            <appMagic:rule name="VerticalAlign" value="%VerticalAlign.RESERVED%.Middle" isExpr="true" />
            <appMagic:rule name="PaddingTop" value="0" />
            <appMagic:rule name="PaddingBottom" value="0" />
          </appMagic:rules>
        </appMagic:control>
      </appMagic:controls>
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="Width" defaultValue="344" />
        <appMagic:overrideProperty name="Height" defaultValue="643" />
        <appMagic:overrideProperty name="Layout" defaultValue="%Layout.RESERVED%.Vertical" isExpr="true" />
        <appMagic:overrideProperty name="TemplateSize" defaultValue="If(Self.Layout = %Layout.RESERVED%.Horizontal, Min(Self.Height, Self.Width - 60), Min(Self.Width, Self.Height - 60))" isExpr="true" />
        <appMagic:overrideProperty name="TemplatePadding" defaultValue="5" />
        <appMagic:overrideProperty name="Items">
          <appMagic:sampleDataSource name="ImageGallerySample" location="data/ImageGallery/ImageGallerySample.xlsx" />
        </appMagic:overrideProperty>
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
    <appMagic:controlVariant name="cardStackGalleryVertical">
      <appMagic:controls>
        <appMagic:control metaDataId="%FluidGrid.ID%" template="FluidGrid" localizedName="##variantControl_CardStack##">
          <appMagic:rules>
            <appMagic:rule name="X" value="0" />
            <appMagic:rule name="Y" value="0" />
            <appMagic:rule name="Width" value="%PARENTCONTROL.ID%.TemplateWidth" isExpr="true" />
            <appMagic:rule name="Height" value="%PARENTCONTROL.ID%.TemplateHeight" isExpr="true" />
            <appMagic:rule name="BorderThickness" value="0" />
            <appMagic:rule name="BorderStyle" value="%BorderStyle.RESERVED%.None" isExpr="true" />
          </appMagic:rules>
        </appMagic:control>
      </appMagic:controls>
      <appMagic:overrideProperties>
        <appMagic:overrideProperty name="X" defaultValue="0" />
        <appMagic:overrideProperty name="Y" defaultValue="0" />
        <appMagic:overrideProperty name="Width" defaultValue="Parent.Width" phoneDefaultValue="Parent.Width" isExpr="true" />
        <appMagic:overrideProperty name="Height" defaultValue="500" phoneDefaultValue="800" />
        <appMagic:overrideProperty name="BorderThickness" defaultValue="2" />
        <appMagic:overrideProperty name="BorderStyle" defaultValue="%BorderStyle.RESERVED%.Solid" isExpr="true" />
        <appMagic:overrideProperty name="BorderColor" defaultValue="%Color.RESERVED%.Black" isExpr="true" />
        <appMagic:overrideProperty name="Layout" defaultValue="%Layout.RESERVED%.Horizontal" isExpr="true" />
        <appMagic:overrideProperty name="TemplatePadding" defaultValue="0" />
        <appMagic:overrideProperty name="TemplateSize" defaultValue="Max(Self.Height, Self.Width)" isExpr="true" />
        <appMagic:overrideProperty name="ShowScrollbar" defaultValue="false" />
        <appMagic:overrideProperty name="Items">
          <appMagic:sampleDataSource name="CardStackGallerySample" location="data/CardStackGallerySample.xlsx" />
        </appMagic:overrideProperty>
      </appMagic:overrideProperties>
    </appMagic:controlVariant>
  </appMagic:controlVariants>
  <appMagic:propertyDependencies>
    <appMagic:propertyDependency input="Items" output="AllItems" />
    <appMagic:propertyDependency input="Items" output="AllItemsCount" />
    <appMagic:propertyDependency input="Items" output="Selected" />
    <appMagic:propertyDependency input="Default" output="Selected" />
    <appMagic:propertyDependency input="Reset" output="Selected" />
    <appMagic:propertyDependency input="SelectionTracksMove" output="Selected" />
    <appMagic:propertyDependency input="TemplateSize" output="TemplateWidth" />
    <appMagic:propertyDependency input="TemplateSize" output="TemplateHeight" />
    <appMagic:propertyDependency input="AutoHeight" output="Height" />
  </appMagic:propertyDependencies>
  <appMagic:conversion from="2.0.0" to="2.1.0">
    <appMagic:conversionAction type="add" name="AccessibleLabel" />
  </appMagic:conversion>
  <appMagic:conversion from="2.1.0" to="2.2.0">
    <appMagic:conversionAction type="remove" name="Snap" />
  </appMagic:conversion>
  <appMagic:conversion from="2.2.0" to="2.3.0">
    <appMagic:conversionAction type="add" name="Reset" />
  </appMagic:conversion>
  <appMagic:conversion from="2.3.0" to="2.4.0">
    <appMagic:conversionAction type="add" name="TemplateMaximumWidth" />
    <appMagic:conversionAction type="add" name="HorizontalScrollPosition" />
  </appMagic:conversion>
  <appMagic:conversion from="2.4.0" to="2.5.0">
    <appMagic:conversionAction type="add" name="Selectable" />
  </appMagic:conversion>
  <appMagic:conversion from="2.5.0" to="2.6.0">
    <appMagic:conversionAction type="add" name="AutoHeight" />
  </appMagic:conversion>
  <appMagic:conversion from="2.6.0" to="2.7.0"></appMagic:conversion>
  <appMagic:conversion from="2.7.0" to="2.8.0">
    <appMagic:conversionAction type="add" name="DelayItemLoading" />
    <appMagic:conversionAction type="add" name="LoadingSpinner" />
    <appMagic:conversionAction type="add" name="LoadingSpinnerColor" />
  </appMagic:conversion>
  <appMagic:conversion from="2.8.0" to="2.9.0">
    <appMagic:conversionAction type="add" name="TabIndex" />
  </appMagic:conversion>
  <appMagic:conversion from="2.9.0" to="2.10.0">
    <!-- Removed HTML template since Knockout Gallery has been removed and it is not used by React Gallery -->
  </appMagic:conversion>
  <appMagic:conversion from="2.10.0" to="2.11.0">
    <appMagic:conversionAction type="add" name="FocusedBorderColor" />
    <appMagic:conversionAction type="add" name="FocusedBorderThickness" />
  </appMagic:conversion>
  <appMagic:conversion from="2.11.0" to="2.11.1">
    <!-- Defined Items property as able to receive record errors -->
  </appMagic:conversion>
  <appMagic:conversion from="2.11.1" to="2.12.0">
    <appMagic:conversionAction type="add" name="ContentLanguage" />
  </appMagic:conversion>
  <appMagic:conversion from="2.12.0" to="2.13.0">
    <!-- Adding showInCommandBar flag -->
  </appMagic:conversion>
  <appMagic:conversion from="2.13.0" to="2.13.1">
    <!-- Adding showInCommandBar flag to additional properties -->
  </appMagic:conversion>
  <appMagic:conversion from="2.13.1" to="2.13.2">
    <!-- Adding showInCommandBar flag to Fields -->
  </appMagic:conversion>
  <appMagic:conversion from="2.13.2" to="2.14.0">
    <appMagic:conversionAction type="add" name="MaxTemplateSize" />
  </appMagic:conversion>
  <appMagic:conversion from="2.14.0" to="2.14.1">
    <!-- Adding showInCanvasInlineActionBar flag-->
  </appMagic:conversion>
  <appMagic:conversion from="2.14.1" to="2.15.0">
    <appMagic:conversionAction type="add" name="#CopilotOverlayLabel" />
    <appMagic:conversionAction type="add" name="#CopilotOverlayVisible" />
  </appMagic:conversion>
</widget>